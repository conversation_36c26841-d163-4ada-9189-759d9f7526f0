{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Sales" %} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Sales" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <form method="get" class="d-flex">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="{% translate 'Search sales...' %}" value="{{ search_query }}">
                <button class="btn btn-primary" type="submit">
                    <i class="fas fa-search"></i> {% translate "Search" %}
                </button>
            </div>
        </form>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'sales:new_sale' %}" class="btn btn-success">
            <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "New Sale" %}
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">{% translate "Filter Sales" %}</h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">{% translate "Status" %}</label>
                <select name="status" id="status" class="form-select">
                    <option value="">{% translate "All Statuses" %}</option>
                    <option value="completed" {% if status == 'completed' %}selected{% endif %}>{% translate "Completed" %}</option>
                    <option value="refunded" {% if status == 'refunded' %}selected{% endif %}>{% translate "Refunded" %}</option>
                    <option value="partially_refunded" {% if status == 'partially_refunded' %}selected{% endif %}>{% translate "Partially Refunded" %}</option>
                    <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>{% translate "Cancelled" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">{% translate "Date From" %}</label>
                <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from }}">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">{% translate "Date To" %}</label>
                <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to }}">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Apply Filters" %}
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{% translate "Sales List" %}</h5>
        <span class="badge bg-primary">{{ sales|length }} {% translate "sales" %}</span>
    </div>
    <div class="card-body">
        {% if sales %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% translate "Invoice #" %}</th>
                        <th>{% translate "Date" %}</th>
                        <th>{% translate "Customer" %}</th>
                        <th>{% translate "Payment Method" %}</th>
                        <th>{% translate "Total" %}</th>
                        <th>{% translate "Status" %}</th>
                        <th>{% translate "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in sales %}
                    <tr>
                        <td>
                            <a href="{% url 'sales:sale_detail' sale.id %}">{{ sale.invoice_number }}</a>
                        </td>
                        <td>{{ sale.created_at|date:"M d, Y H:i" }}</td>
                        <td>
                            {% if sale.customer_name %}
                            {{ sale.customer_name }}
                            {% else %}
                            <span class="text-muted">{% translate "Walk-in Customer" %}</span>
                            {% endif %}
                        </td>
                        <td>{{ sale.get_payment_method_display }}</td>
                        <td>${{ sale.total|floatformat:2 }}</td>
                        <td>
                            {% if sale.status == 'completed' %}
                            <span class="badge bg-success">{% translate "Completed" %}</span>
                            {% elif sale.status == 'refunded' %}
                            <span class="badge bg-danger">{% translate "Refunded" %}</span>
                            {% elif sale.status == 'partially_refunded' %}
                            <span class="badge bg-warning">{% translate "Partially Refunded" %}</span>
                            {% elif sale.status == 'cancelled' %}
                            <span class="badge bg-secondary">{% translate "Cancelled" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'sales:sale_detail' sale.id %}" class="btn btn-sm btn-info text-white">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'sales:generate_invoice' sale.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-file-invoice"></i>
                                </a>
                                {% if sale.status == 'completed' %}
                                <a href="{% url 'sales:refund_sale' sale.id %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-undo"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            {% if search_query or status or date_from or date_to %}
            {% translate "No sales found matching your search criteria." %}
            {% else %}
            {% translate "No sales found. Create your first sale using the 'New Sale' button." %}
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
