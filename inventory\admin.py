from django.contrib import admin
from .models import Inventory, StockMovement

class StockMovementInline(admin.TabularInline):
    model = StockMovement
    extra = 0
    readonly_fields = ('created_at',)

@admin.register(Inventory)
class InventoryAdmin(admin.ModelAdmin):
    list_display = ('product', 'quantity', 'reorder_level', 'is_low_stock', 'last_restock_date')
    list_filter = ('last_restock_date',)
    search_fields = ('product__name', 'product__sku')
    inlines = [StockMovementInline]
    readonly_fields = ('created_at', 'updated_at')

@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    list_display = ('inventory', 'quantity', 'movement_type', 'created_at', 'created_by')
    list_filter = ('movement_type', 'created_at')
    search_fields = ('inventory__product__name', 'notes')
    readonly_fields = ('created_at',)
