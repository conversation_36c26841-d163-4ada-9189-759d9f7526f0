from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db import models
from .models import Product, Category
from inventory.models import Inventory

@login_required
def product_list(request):
    products = Product.objects.all()
    categories = Category.objects.all()

    # Filter by category if provided
    category_id = request.GET.get('category')
    if category_id:
        products = products.filter(category_id=category_id)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        products = products.filter(
            models.Q(name__icontains=search_query) |
            models.Q(description__icontains=search_query) |
            models.Q(sku__icontains=search_query) |
            models.Q(barcode__icontains=search_query)
        )

    context = {
        'products': products,
        'categories': categories,
        'selected_category': category_id,
        'search_query': search_query,
    }

    return render(request, 'products/product_list.html', context)

@login_required
def product_detail(request, pk):
    product = get_object_or_404(Product, pk=pk)

    # Get inventory information
    try:
        inventory = Inventory.objects.get(product=product)
    except Inventory.DoesNotExist:
        inventory = None

    context = {
        'product': product,
        'inventory': inventory,
    }

    return render(request, 'products/product_detail.html', context)

@login_required
def product_add(request):
    categories = Category.objects.all()

    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        sku = request.POST.get('sku')
        barcode = request.POST.get('barcode', '')
        category_id = request.POST.get('category')
        price = request.POST.get('price')
        cost_price = request.POST.get('cost_price')
        initial_stock = request.POST.get('initial_stock', 0)
        reorder_level = request.POST.get('reorder_level', 10)

        # Validate required fields
        if not all([name, sku, price, cost_price]):
            messages.error(request, 'Please fill in all required fields')
            return redirect('products:product_add')

        # Check if SKU already exists
        if Product.objects.filter(sku=sku).exists():
            messages.error(request, 'A product with this SKU already exists')
            return redirect('products:product_add')

        # Create the product
        product = Product.objects.create(
            name=name,
            description=description,
            sku=sku,
            barcode=barcode,
            category_id=category_id if category_id else None,
            price=price,
            cost_price=cost_price,
        )

        # Handle image upload
        if 'image' in request.FILES:
            product.image = request.FILES['image']
            product.save()

        # Create inventory record
        Inventory.objects.create(
            product=product,
            quantity=initial_stock,
            reorder_level=reorder_level
        )

        messages.success(request, f'Product "{name}" added successfully')
        return redirect('products:product_detail', pk=product.pk)

    context = {
        'categories': categories,
    }

    return render(request, 'products/product_add.html', context)

@login_required
def product_edit(request, pk):
    product = get_object_or_404(Product, pk=pk)
    categories = Category.objects.all()

    try:
        inventory = Inventory.objects.get(product=product)
    except Inventory.DoesNotExist:
        inventory = None

    if request.method == 'POST':
        product.name = request.POST.get('name')
        product.description = request.POST.get('description', '')
        product.sku = request.POST.get('sku')
        product.barcode = request.POST.get('barcode', '')
        product.category_id = request.POST.get('category') or None
        product.price = request.POST.get('price')
        product.cost_price = request.POST.get('cost_price')
        product.is_active = request.POST.get('is_active') == 'on'

        # Handle image upload
        if 'image' in request.FILES:
            product.image = request.FILES['image']

        product.save()

        # Update inventory
        if inventory:
            inventory.reorder_level = request.POST.get('reorder_level', inventory.reorder_level)
            inventory.save()

        messages.success(request, f'Product "{product.name}" updated successfully')
        return redirect('products:product_detail', pk=product.pk)

    context = {
        'product': product,
        'inventory': inventory,
        'categories': categories,
    }

    return render(request, 'products/product_edit.html', context)

@login_required
def product_delete(request, pk):
    product = get_object_or_404(Product, pk=pk)

    if request.method == 'POST':
        product_name = product.name
        product.delete()
        messages.success(request, f'Product "{product_name}" deleted successfully')
        return redirect('products:product_list')

    context = {
        'product': product,
    }

    return render(request, 'products/product_delete.html', context)

@login_required
def category_list(request):
    categories = Category.objects.all()

    context = {
        'categories': categories,
    }

    return render(request, 'products/category_list.html', context)

@login_required
def category_add(request):
    categories = Category.objects.all()  # For parent category selection

    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        parent_id = request.POST.get('parent')

        if not name:
            messages.error(request, 'Category name is required')
            return redirect('products:category_add')

        Category.objects.create(
            name=name,
            description=description,
            parent_id=parent_id if parent_id else None
        )

        messages.success(request, f'Category "{name}" added successfully')
        return redirect('products:category_list')

    context = {
        'categories': categories,
    }

    return render(request, 'products/category_add.html', context)

@login_required
def category_edit(request, pk):
    category = get_object_or_404(Category, pk=pk)
    categories = Category.objects.exclude(pk=pk)  # Exclude self from parent options

    if request.method == 'POST':
        category.name = request.POST.get('name')
        category.description = request.POST.get('description', '')
        parent_id = request.POST.get('parent')

        # Prevent circular reference
        if parent_id and int(parent_id) != category.pk:
            category.parent_id = parent_id
        else:
            category.parent = None

        category.save()
        messages.success(request, f'Category "{category.name}" updated successfully')
        return redirect('products:category_list')

    context = {
        'category': category,
        'categories': categories,
    }

    return render(request, 'products/category_edit.html', context)

@login_required
def category_delete(request, pk):
    category = get_object_or_404(Category, pk=pk)

    if request.method == 'POST':
        # Check if category has products
        if category.products.exists():
            messages.error(request, f'Cannot delete category "{category.name}" because it has products associated with it')
            return redirect('products:category_list')

        category_name = category.name
        category.delete()
        messages.success(request, f'Category "{category_name}" deleted successfully')
        return redirect('products:category_list')

    context = {
        'category': category,
        'has_products': category.products.exists(),
    }

    return render(request, 'products/category_delete.html', context)
