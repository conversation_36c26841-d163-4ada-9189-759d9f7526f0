{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Add Inventory" %} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Add Inventory Record" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">{% translate "Inventory" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% translate "Add Inventory Record" %}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'inventory:inventory_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Back to Inventory" %}
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{% translate "Add New Inventory Record" %}</h5>
    </div>
    <div class="card-body">
        {% if products %}
        <form method="post">
            {% csrf_token %}
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="product" class="form-label">{% translate "Select Product" %} <span class="text-danger">*</span></label>
                        <select class="form-select" id="product" name="product" required>
                            <option value="">{% translate "Choose a product..." %}</option>
                            {% for product in products %}
                            <option value="{{ product.id }}">{{ product.name }} ({{ product.sku }})</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">{% translate "Only products without existing inventory records are shown" %}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quantity" class="form-label">{% translate "Initial Quantity" %}</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="0" value="0">
                        <div class="form-text">{% translate "The starting quantity for this product" %}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reorder_level" class="form-label">{% translate "Reorder Level" %}</label>
                        <input type="number" class="form-control" id="reorder_level" name="reorder_level" min="0" value="10">
                        <div class="form-text">{% translate "Minimum stock level before reordering" %}</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">{% translate "What is a Reorder Level?" %}</h6>
                            <p class="card-text">
                                {% translate "The reorder level is the minimum quantity of a product that should be in stock. When the stock level falls below this number, it's time to order more." %}
                            </p>
                            <p class="card-text">
                                {% translate "Setting an appropriate reorder level helps you:" %}
                            </p>
                            <ul>
                                <li>{% translate "Avoid running out of stock" %}</li>
                                <li>{% translate "Maintain optimal inventory levels" %}</li>
                                <li>{% translate "Plan purchases efficiently" %}</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                        {% translate "If you set an initial quantity greater than zero, a stock movement record will be created automatically." %}
                    </div>
                </div>
            </div>
            
            <div class="border-top pt-3 text-end">
                <button type="button" class="btn btn-secondary" onclick="window.location.href='{% url 'inventory:inventory_list' %}'">
                    {% translate "Cancel" %}
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Create Inventory Record" %}
                </button>
            </div>
        </form>
        {% else %}
        <div class="alert alert-warning">
            <h5 class="alert-heading">{% translate "No Products Available" %}</h5>
            <p>{% translate "There are no products without inventory records available." %}</p>
            <hr>
            <p class="mb-0">
                {% translate "You can" %}:
                <ul>
                    <li><a href="{% url 'products:product_add' %}">{% translate "Add a new product" %}</a></li>
                    <li><a href="{% url 'inventory:inventory_list' %}">{% translate "View existing inventory" %}</a></li>
                </ul>
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
