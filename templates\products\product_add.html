{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Add Product" %} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Add New Product" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'products:product_list' %}">{% translate "Products" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% translate "Add New Product" %}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'products:product_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Back to Products" %}
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{% translate "Product Information" %}</h5>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="row mb-4">
                <div class="col-md-8">
                    <h6 class="mb-3">{% translate "Basic Information" %}</h6>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">{% translate "Product Name" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sku" class="form-label">{% translate "SKU" %} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="sku" name="sku" required>
                                <div class="form-text">{% translate "Unique product identifier" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="barcode" class="form-label">{% translate "Barcode" %}</label>
                                <input type="text" class="form-control" id="barcode" name="barcode">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">{% translate "Category" %}</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">{% translate "Select Category" %}</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">{% translate "Description" %}</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <h6 class="mb-3 mt-4">{% translate "Pricing Information" %}</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">{% translate "Selling Price" %} <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">{% translate "Cost Price" %} <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="cost_price" name="cost_price" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h6 class="mb-3 mt-4">{% translate "Inventory Information" %}</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="initial_stock" class="form-label">{% translate "Initial Stock" %}</label>
                                <input type="number" class="form-control" id="initial_stock" name="initial_stock" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reorder_level" class="form-label">{% translate "Reorder Level" %}</label>
                                <input type="number" class="form-control" id="reorder_level" name="reorder_level" min="0" value="10">
                                <div class="form-text">{% translate "Minimum stock level before reordering" %}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <h6 class="mb-3">{% translate "Product Image" %}</h6>
                    
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <img id="image-preview" src="#" alt="{% translate 'Preview' %}" class="img-fluid mb-2" style="max-height: 200px; display: none;">
                                <div id="no-image" class="bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-image fa-4x text-muted"></i>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="image" class="form-label">{% translate "Upload Image" %}</label>
                                <input class="form-control" type="file" id="image" name="image" accept="image/*">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">{% translate "Product is active" %}</label>
                    </div>
                </div>
            </div>
            
            <div class="border-top pt-3 text-end">
                <button type="button" class="btn btn-secondary" onclick="window.location.href='{% url 'products:product_list' %}'">
                    {% translate "Cancel" %}
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Save Product" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('image-preview');
                preview.src = e.target.result;
                preview.style.display = 'block';
                document.getElementById('no-image').style.display = 'none';
            }
            reader.readAsDataURL(file);
        } else {
            document.getElementById('image-preview').style.display = 'none';
            document.getElementById('no-image').style.display = 'flex';
        }
    });
</script>
{% endblock %}
