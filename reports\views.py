from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db import models
from django.http import HttpResponse
from django.utils import timezone
import csv
import datetime
from .models import Report
from sales.models import Sale, SaleItem
from products.models import Product
from inventory.models import Inventory, StockMovement

@login_required
def reports_dashboard(request):
    # Get counts
    total_sales = Sale.objects.count()
    total_products = Product.objects.count()
    total_inventory = Inventory.objects.count()

    # Get sales by status
    sales_by_status = Sale.objects.values('status').annotate(count=models.Count('id'))

    # Get recent reports
    recent_reports = Report.objects.all().order_by('-created_at')[:5]

    context = {
        'total_sales': total_sales,
        'total_products': total_products,
        'total_inventory': total_inventory,
        'sales_by_status': sales_by_status,
        'recent_reports': recent_reports,
    }

    return render(request, 'reports/reports_dashboard.html', context)

@login_required
def sales_report(request):
    # Default to current month
    today = timezone.now().date()
    first_day_of_month = today.replace(day=1)

    # Get date range from request or use defaults
    date_from = request.GET.get('date_from', first_day_of_month.strftime('%Y-%m-%d'))
    date_to = request.GET.get('date_to', today.strftime('%Y-%m-%d'))

    # Convert to datetime objects
    try:
        date_from_obj = datetime.datetime.strptime(date_from, '%Y-%m-%d').date()
        date_to_obj = datetime.datetime.strptime(date_to, '%Y-%m-%d').date()
    except ValueError:
        date_from_obj = first_day_of_month
        date_to_obj = today
        date_from = date_from_obj.strftime('%Y-%m-%d')
        date_to = date_to_obj.strftime('%Y-%m-%d')

    # Get sales in date range
    sales = Sale.objects.filter(
        created_at__date__gte=date_from_obj,
        created_at__date__lte=date_to_obj
    ).order_by('-created_at')

    # Calculate totals
    total_sales_count = sales.count()
    total_revenue = sales.filter(status='completed').aggregate(total=models.Sum('total'))['total'] or 0

    # Sales by payment method
    sales_by_payment = sales.values('payment_method').annotate(
        count=models.Count('id'),
        total=models.Sum('total')
    )

    # Sales by status
    sales_by_status = sales.values('status').annotate(
        count=models.Count('id'),
        total=models.Sum('total')
    )

    # Top selling products
    top_products = SaleItem.objects.filter(
        sale__created_at__date__gte=date_from_obj,
        sale__created_at__date__lte=date_to_obj,
        sale__status='completed'
    ).values(
        'product__name'
    ).annotate(
        total_quantity=models.Sum('quantity'),
        total_revenue=models.Sum(models.F('price') * models.F('quantity'))
    ).order_by('-total_quantity')[:10]

    context = {
        'sales': sales,
        'date_from': date_from,
        'date_to': date_to,
        'total_sales_count': total_sales_count,
        'total_revenue': total_revenue,
        'sales_by_payment': sales_by_payment,
        'sales_by_status': sales_by_status,
        'top_products': top_products,
    }

    return render(request, 'reports/sales_report.html', context)

@login_required
def inventory_report(request):
    # Get all inventory items
    inventory_items = Inventory.objects.all().select_related('product')

    # Calculate inventory value
    total_value = sum(item.quantity * item.product.cost_price for item in inventory_items)

    # Low stock items
    low_stock_items = inventory_items.filter(quantity__lte=models.F('reorder_level'))

    # Out of stock items
    out_of_stock_items = inventory_items.filter(quantity=0)

    # Recent stock movements
    recent_movements = StockMovement.objects.all().order_by('-created_at')[:20]

    context = {
        'inventory_items': inventory_items,
        'total_value': total_value,
        'low_stock_items': low_stock_items,
        'low_stock_count': low_stock_items.count(),
        'out_of_stock_items': out_of_stock_items,
        'out_of_stock_count': out_of_stock_items.count(),
        'recent_movements': recent_movements,
    }

    return render(request, 'reports/inventory_report.html', context)

@login_required
def products_report(request):
    # Get all products
    products = Product.objects.all()

    # Products by category
    products_by_category = products.values('category__name').annotate(count=models.Count('id'))

    # Products with no inventory
    products_without_inventory = Product.objects.exclude(
        id__in=Inventory.objects.values_list('product_id', flat=True)
    )

    # Inactive products
    inactive_products = products.filter(is_active=False)

    context = {
        'products': products,
        'products_by_category': products_by_category,
        'products_without_inventory': products_without_inventory,
        'inactive_products': inactive_products,
    }

    return render(request, 'reports/products_report.html', context)

@login_required
def export_sales_report(request):
    # Get date range from request
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    # Convert to datetime objects
    try:
        date_from_obj = datetime.datetime.strptime(date_from, '%Y-%m-%d').date()
        date_to_obj = datetime.datetime.strptime(date_to, '%Y-%m-%d').date()
    except (ValueError, TypeError):
        # Default to current month
        today = timezone.now().date()
        date_to_obj = today
        date_from_obj = today.replace(day=1)

    # Get sales in date range
    sales = Sale.objects.filter(
        created_at__date__gte=date_from_obj,
        created_at__date__lte=date_to_obj
    ).order_by('-created_at')

    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="sales_report_{date_from_obj}_to_{date_to_obj}.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Invoice Number', 'Date', 'Customer', 'Payment Method',
        'Status', 'Subtotal', 'Discount', 'Tax', 'Total'
    ])

    for sale in sales:
        writer.writerow([
            sale.invoice_number,
            sale.created_at.strftime('%Y-%m-%d %H:%M'),
            sale.customer_name or 'Walk-in Customer',
            sale.get_payment_method_display(),
            sale.get_status_display(),
            sale.subtotal,
            sale.discount,
            sale.tax,
            sale.total
        ])

    # Save report record
    Report.objects.create(
        report_type='sales',
        title=f'Sales Report {date_from_obj} to {date_to_obj}',
        date_from=date_from_obj,
        date_to=date_to_obj,
        created_by=request.user.profile
    )

    return response

@login_required
def export_inventory_report(request):
    # Get all inventory items
    inventory_items = Inventory.objects.all().select_related('product')

    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="inventory_report_{timezone.now().date()}.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Product', 'SKU', 'Category', 'Quantity', 'Reorder Level',
        'Cost Price', 'Selling Price', 'Value', 'Last Restock Date'
    ])

    for item in inventory_items:
        writer.writerow([
            item.product.name,
            item.product.sku,
            item.product.category.name if item.product.category else 'Uncategorized',
            item.quantity,
            item.reorder_level,
            item.product.cost_price,
            item.product.price,
            item.quantity * item.product.cost_price,
            item.last_restock_date.strftime('%Y-%m-%d') if item.last_restock_date else 'N/A'
        ])

    # Save report record
    today = timezone.now().date()
    Report.objects.create(
        report_type='inventory',
        title=f'Inventory Report {today}',
        date_from=today,
        date_to=today,
        created_by=request.user.profile
    )

    return response
