from django.db import models
from django.utils import timezone
from products.models import Product

class Inventory(models.Model):
    product = models.OneToOneField(Product, on_delete=models.CASCADE, related_name='inventory')
    quantity = models.PositiveIntegerField(default=0)
    reorder_level = models.PositiveIntegerField(default=10)
    last_restock_date = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = 'Inventory'

    def __str__(self):
        return f"{self.product.name} - Qty: {self.quantity}"

    @property
    def is_low_stock(self):
        return self.quantity <= self.reorder_level

    def restock(self, quantity, user=None):
        """Add stock to inventory"""
        self.quantity += quantity
        self.last_restock_date = timezone.now()
        self.save()

        # Create a stock movement record
        StockMovement.objects.create(
            inventory=self,
            quantity=quantity,
            movement_type='restock',
            created_by=user
        )

        return True

class StockMovement(models.Model):
    MOVEMENT_TYPES = (
        ('restock', 'Restock'),
        ('sale', 'Sale'),
        ('return', 'Return'),
        ('adjustment', 'Adjustment'),
        ('damaged', 'Damaged/Lost'),
    )

    inventory = models.ForeignKey(Inventory, on_delete=models.CASCADE, related_name='movements')
    quantity = models.IntegerField()  # Can be negative for sales, damaged, etc.
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey('accounts.UserProfile', on_delete=models.SET_NULL, blank=True, null=True)

    def __str__(self):
        return f"{self.get_movement_type_display()} - {self.inventory.product.name} - {self.quantity}"
