from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.db import models
from .models import UserProfile
from sales.models import Sale
from inventory.models import Inventory
from products.models import Product

def login_view(request):
    if request.user.is_authenticated:
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)
            return redirect('accounts:dashboard')
        else:
            messages.error(request, 'Invalid username or password')

    return render(request, 'accounts/login.html')

def logout_view(request):
    logout(request)
    return redirect('accounts:login')

def register(request):
    if request.user.is_authenticated:
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        password2 = request.POST.get('password2')
        role = request.POST.get('role', 'cashier')

        if password != password2:
            messages.error(request, 'Passwords do not match')
            return redirect('accounts:register')

        if User.objects.filter(username=username).exists():
            messages.error(request, 'Username already exists')
            return redirect('accounts:register')

        if User.objects.filter(email=email).exists():
            messages.error(request, 'Email already exists')
            return redirect('accounts:register')

        user = User.objects.create_user(username=username, email=email, password=password)
        UserProfile.objects.create(user=user, role=role)

        messages.success(request, 'Account created successfully. You can now login.')
        return redirect('accounts:login')

    return render(request, 'accounts/register.html')

@login_required
def dashboard(request):
    # Get recent sales
    recent_sales = Sale.objects.all().order_by('-created_at')[:5]

    # Get low stock items
    low_stock_items = Inventory.objects.filter(quantity__lte=models.F('reorder_level'))[:5]

    # Get total products, sales, and revenue
    total_products = Product.objects.count()
    total_sales = Sale.objects.count()
    total_revenue = Sale.objects.filter(status='completed').aggregate(total=models.Sum('total'))['total'] or 0

    context = {
        'recent_sales': recent_sales,
        'low_stock_items': low_stock_items,
        'total_products': total_products,
        'total_sales': total_sales,
        'total_revenue': total_revenue,
    }

    return render(request, 'accounts/dashboard.html', context)

@login_required
def profile(request):
    user_profile = request.user.profile

    if request.method == 'POST':
        # Update user profile
        user = request.user
        user.first_name = request.POST.get('first_name', user.first_name)
        user.last_name = request.POST.get('last_name', user.last_name)
        user.email = request.POST.get('email', user.email)
        user.save()

        # Update profile
        user_profile.phone = request.POST.get('phone', user_profile.phone)
        user_profile.address = request.POST.get('address', user_profile.address)

        # Handle profile image upload
        if 'profile_image' in request.FILES:
            user_profile.profile_image = request.FILES['profile_image']

        user_profile.save()
        messages.success(request, 'Profile updated successfully')
        return redirect('accounts:profile')

    context = {
        'user_profile': user_profile
    }

    return render(request, 'accounts/profile.html', context)
