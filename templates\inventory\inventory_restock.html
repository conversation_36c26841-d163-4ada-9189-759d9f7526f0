{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Restock" %} {{ inventory.product.name }} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Restock Inventory" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">{% translate "Inventory" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_detail' inventory.id %}">{{ inventory.product.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% translate "Restock" %}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'inventory:inventory_detail' inventory.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Back to Inventory" %}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Restock" %} {{ inventory.product.name }}</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="product-name" class="form-label">{% translate "Product" %}</label>
                                <input type="text" class="form-control" id="product-name" value="{{ inventory.product.name }}" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="current-stock" class="form-label">{% translate "Current Stock" %}</label>
                                <input type="text" class="form-control" id="current-stock" value="{{ inventory.quantity }}" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="quantity" class="form-label">{% translate "Quantity to Add" %} <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="quantity" name="quantity" min="1" value="1" required>
                                <div class="form-text">{% translate "Enter the number of units to add to the current stock" %}</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">{% translate "Notes" %}</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="{% translate 'Optional notes about this restock' %}"></textarea>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">{% translate "Product Information" %}</h6>
                                    
                                    <div class="text-center mb-3">
                                        {% if inventory.product.image %}
                                        <img src="{{ inventory.product.image.url }}" alt="{{ inventory.product.name }}" class="img-fluid mb-2" style="max-height: 100px;">
                                        {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center mb-2" style="height: 100px;">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-5">{% translate "SKU" %}:</div>
                                        <div class="col-7 text-end">{{ inventory.product.sku }}</div>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-5">{% translate "Category" %}:</div>
                                        <div class="col-7 text-end">{{ inventory.product.category.name|default:"—" }}</div>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-5">{% translate "Reorder Level" %}:</div>
                                        <div class="col-7 text-end">{{ inventory.reorder_level }}</div>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-5">{% translate "Status" %}:</div>
                                        <div class="col-7 text-end">
                                            {% if inventory.quantity == 0 %}
                                            <span class="badge bg-danger">{% translate "Out of Stock" %}</span>
                                            {% elif inventory.quantity <= inventory.reorder_level %}
                                            <span class="badge bg-warning">{% translate "Low Stock" %}</span>
                                            {% else %}
                                            <span class="badge bg-success">{% translate "In Stock" %}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                                {% translate "A stock movement record will be created automatically when you restock this item." %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="border-top pt-3 text-end">
                        <button type="button" class="btn btn-secondary" onclick="window.location.href='{% url 'inventory:inventory_detail' inventory.id %}'">
                            {% translate "Cancel" %}
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Restock Inventory" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Recent Stock Movements" %}</h5>
            </div>
            <div class="card-body">
                {% if stock_movements %}
                <div class="list-group">
                    {% for movement in stock_movements %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                {% if movement.movement_type == 'restock' %}
                                <span class="badge bg-success">{% translate "Restock" %}</span>
                                {% elif movement.movement_type == 'sale' %}
                                <span class="badge bg-primary">{% translate "Sale" %}</span>
                                {% elif movement.movement_type == 'return' %}
                                <span class="badge bg-warning">{% translate "Return" %}</span>
                                {% elif movement.movement_type == 'adjustment' %}
                                <span class="badge bg-info">{% translate "Adjustment" %}</span>
                                {% elif movement.movement_type == 'damaged' %}
                                <span class="badge bg-danger">{% translate "Damaged/Lost" %}</span>
                                {% endif %}
                            </h6>
                            <small>{{ movement.created_at|date:"M d, Y" }}</small>
                        </div>
                        <p class="mb-1">
                            {% if movement.quantity > 0 %}
                            <span class="text-success">+{{ movement.quantity }}</span>
                            {% else %}
                            <span class="text-danger">{{ movement.quantity }}</span>
                            {% endif %}
                            {% if movement.notes %}
                            - {{ movement.notes }}
                            {% endif %}
                        </p>
                        <small>
                            {% if movement.created_by %}
                            {{ movement.created_by.user.username }}
                            {% else %}
                            <span class="text-muted">{% translate "System" %}</span>
                            {% endif %}
                        </small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    {% translate "No recent stock movements for this product." %}
                </div>
                {% endif %}
                
                <div class="d-grid mt-3">
                    <a href="{% url 'inventory:inventory_detail' inventory.id %}" class="btn btn-outline-primary btn-sm">
                        {% translate "View All Stock Movements" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
