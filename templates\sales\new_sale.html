{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "New Sale" %} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "New Sale" %}{% endblock %}

{% block extra_css %}
<style>
    .product-card {
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .product-image {
        height: 100px;
        object-fit: contain;
        margin-bottom: 10px;
    }
    
    .cart-item {
        padding: 10px;
        border-bottom: 1px solid #eee;
    }
    
    .cart-item:last-child {
        border-bottom: none;
    }
    
    .cart-empty {
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }
    
    #cart-container {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .category-pills {
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 10px;
    }
    
    .category-pill {
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 10px;
    }
    
    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }
    
    @media (max-width: 768px) {
        .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Products Section -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{% translate "Products" %}</h5>
                <div class="input-group" style="max-width: 300px;">
                    <input type="text" id="product-search" class="form-control" placeholder="{% translate 'Search products...' %}">
                    <button class="btn btn-outline-secondary" type="button" id="clear-search">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Categories -->
                <div class="category-pills mb-3">
                    <button class="btn btn-outline-primary category-pill active" data-category="all">
                        {% translate "All Categories" %}
                    </button>
                    {% for category in categories %}
                    <button class="btn btn-outline-primary category-pill" data-category="{{ category.category_id }}">
                        {{ category.category__name|default:"Uncategorized" }}
                    </button>
                    {% endfor %}
                </div>
                
                <!-- Products Grid -->
                <div class="product-grid" id="products-container">
                    <!-- Products will be loaded here via JavaScript -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cart Section -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Shopping Cart" %}</h5>
            </div>
            <div class="card-body p-0">
                <div id="cart-container">
                    <div class="cart-empty">
                        <div class="text-center">
                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                            <p>{% translate "Your cart is empty" %}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between mb-2">
                    <span>{% translate "Subtotal" %}:</span>
                    <span id="subtotal">$0.00</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>{% translate "Discount" %}:</span>
                    <div class="input-group input-group-sm" style="width: 120px;">
                        <input type="number" id="discount" class="form-control" value="0" min="0" step="0.01">
                        <span class="input-group-text">$</span>
                    </div>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>{% translate "Tax" %}:</span>
                    <div class="input-group input-group-sm" style="width: 120px;">
                        <input type="number" id="tax" class="form-control" value="0" min="0" step="0.01">
                        <span class="input-group-text">$</span>
                    </div>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <span class="fw-bold">{% translate "Total" %}:</span>
                    <span id="total" class="fw-bold">$0.00</span>
                </div>
                <button id="checkout-btn" class="btn btn-success w-100 mb-2" disabled>
                    <i class="fas fa-cash-register {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Checkout" %}
                </button>
                <button id="clear-cart-btn" class="btn btn-outline-danger w-100" disabled>
                    <i class="fas fa-trash {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Clear Cart" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Checkout Modal -->
<div class="modal fade" id="checkout-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% translate "Complete Sale" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="checkout-form">
                    <div class="mb-3">
                        <label for="customer-name" class="form-label">{% translate "Customer Name" %}</label>
                        <input type="text" class="form-control" id="customer-name">
                    </div>
                    <div class="mb-3">
                        <label for="customer-phone" class="form-label">{% translate "Customer Phone" %}</label>
                        <input type="text" class="form-control" id="customer-phone">
                    </div>
                    <div class="mb-3">
                        <label for="customer-email" class="form-label">{% translate "Customer Email" %}</label>
                        <input type="email" class="form-control" id="customer-email">
                    </div>
                    <div class="mb-3">
                        <label for="payment-method" class="form-label">{% translate "Payment Method" %}</label>
                        <select class="form-select" id="payment-method">
                            <option value="cash">{% translate "Cash" %}</option>
                            <option value="card">{% translate "Credit/Debit Card" %}</option>
                            <option value="transfer">{% translate "Bank Transfer" %}</option>
                            <option value="mobile">{% translate "Mobile Payment" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">{% translate "Notes" %}</label>
                        <textarea class="form-control" id="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% translate "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="complete-sale-btn">{% translate "Complete Sale" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Products data from Django
    const products = {{ products|safe }};
    
    // Initialize variables
    let cart = [];
    let subtotal = 0;
    let discount = 0;
    let tax = 0;
    let total = 0;
    
    // DOM elements
    const productsContainer = document.getElementById('products-container');
    const cartContainer = document.getElementById('cart-container');
    const subtotalEl = document.getElementById('subtotal');
    const discountEl = document.getElementById('discount');
    const taxEl = document.getElementById('tax');
    const totalEl = document.getElementById('total');
    const checkoutBtn = document.getElementById('checkout-btn');
    const clearCartBtn = document.getElementById('clear-cart-btn');
    const productSearch = document.getElementById('product-search');
    const clearSearchBtn = document.getElementById('clear-search');
    const categoryPills = document.querySelectorAll('.category-pill');
    
    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        renderProducts(products);
        
        // Event listeners
        discountEl.addEventListener('input', updateTotals);
        taxEl.addEventListener('input', updateTotals);
        clearCartBtn.addEventListener('click', clearCart);
        checkoutBtn.addEventListener('click', showCheckoutModal);
        productSearch.addEventListener('input', filterProducts);
        clearSearchBtn.addEventListener('click', clearSearch);
        
        // Category filter
        categoryPills.forEach(pill => {
            pill.addEventListener('click', function() {
                categoryPills.forEach(p => p.classList.remove('active'));
                this.classList.add('active');
                filterProducts();
            });
        });
        
        // Complete sale button
        document.getElementById('complete-sale-btn').addEventListener('click', completeSale);
    });
    
    // Render products in the grid
    function renderProducts(productsToRender) {
        productsContainer.innerHTML = '';
        
        if (productsToRender.length === 0) {
            productsContainer.innerHTML = `<div class="alert alert-info w-100">{% translate "No products found" %}</div>`;
            return;
        }
        
        productsToRender.forEach(product => {
            const productCard = document.createElement('div');
            productCard.className = 'card product-card';
            productCard.dataset.id = product.id;
            
            const stockBadge = product.stock <= 0 
                ? `<span class="badge bg-danger position-absolute top-0 end-0 m-2">{% translate "Out of Stock" %}</span>`
                : '';
            
            const productImage = product.image 
                ? `<img src="${product.image}" class="product-image" alt="${product.name}">`
                : `<div class="bg-light d-flex align-items-center justify-content-center" style="height: 100px;"><i class="fas fa-image text-muted fa-2x"></i></div>`;
            
            productCard.innerHTML = `
                ${stockBadge}
                <div class="card-body text-center">
                    ${productImage}
                    <h6 class="card-title">${product.name}</h6>
                    <p class="card-text text-primary fw-bold">$${product.price.toFixed(2)}</p>
                    <button class="btn btn-sm btn-outline-primary add-to-cart-btn" ${product.stock <= 0 ? 'disabled' : ''}>
                        <i class="fas fa-plus"></i> {% translate "Add to Cart" %}
                    </button>
                </div>
            `;
            
            productCard.querySelector('.add-to-cart-btn').addEventListener('click', function() {
                addToCart(product);
            });
            
            productsContainer.appendChild(productCard);
        });
    }
    
    // Add product to cart
    function addToCart(product) {
        // Check if product is already in cart
        const existingItem = cart.find(item => item.id === product.id);
        
        if (existingItem) {
            // Check if we have enough stock
            if (existingItem.quantity >= product.stock) {
                alert(`{% translate "Sorry, only" %} ${product.stock} {% translate "units available in stock" %}`);
                return;
            }
            existingItem.quantity += 1;
        } else {
            cart.push({
                id: product.id,
                name: product.name,
                price: product.price,
                quantity: 1,
                stock: product.stock
            });
        }
        
        renderCart();
        updateTotals();
    }
    
    // Render cart items
    function renderCart() {
        if (cart.length === 0) {
            cartContainer.innerHTML = `
                <div class="cart-empty">
                    <div class="text-center">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>{% translate "Your cart is empty" %}</p>
                    </div>
                </div>
            `;
            checkoutBtn.disabled = true;
            clearCartBtn.disabled = true;
            return;
        }
        
        checkoutBtn.disabled = false;
        clearCartBtn.disabled = false;
        
        cartContainer.innerHTML = '';
        
        cart.forEach((item, index) => {
            const cartItem = document.createElement('div');
            cartItem.className = 'cart-item';
            cartItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">${item.name}</h6>
                        <small class="text-muted">$${item.price.toFixed(2)} x ${item.quantity}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold">$${(item.price * item.quantity).toFixed(2)}</div>
                        <div class="btn-group btn-group-sm mt-1">
                            <button class="btn btn-outline-secondary decrease-qty" data-index="${index}">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button class="btn btn-outline-secondary increase-qty" data-index="${index}" ${item.quantity >= item.stock ? 'disabled' : ''}>
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="btn btn-outline-danger remove-item" data-index="${index}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            cartContainer.appendChild(cartItem);
        });
        
        // Add event listeners to cart buttons
        document.querySelectorAll('.decrease-qty').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.dataset.index);
                decreaseQuantity(index);
            });
        });
        
        document.querySelectorAll('.increase-qty').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.dataset.index);
                increaseQuantity(index);
            });
        });
        
        document.querySelectorAll('.remove-item').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.dataset.index);
                removeItem(index);
            });
        });
    }
    
    // Update totals
    function updateTotals() {
        subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        discount = parseFloat(discountEl.value) || 0;
        tax = parseFloat(taxEl.value) || 0;
        total = subtotal - discount + tax;
        
        subtotalEl.textContent = `$${subtotal.toFixed(2)}`;
        totalEl.textContent = `$${total.toFixed(2)}`;
    }
    
    // Decrease item quantity
    function decreaseQuantity(index) {
        if (cart[index].quantity > 1) {
            cart[index].quantity -= 1;
        } else {
            removeItem(index);
            return;
        }
        
        renderCart();
        updateTotals();
    }
    
    // Increase item quantity
    function increaseQuantity(index) {
        if (cart[index].quantity < cart[index].stock) {
            cart[index].quantity += 1;
            renderCart();
            updateTotals();
        }
    }
    
    // Remove item from cart
    function removeItem(index) {
        cart.splice(index, 1);
        renderCart();
        updateTotals();
    }
    
    // Clear cart
    function clearCart() {
        cart = [];
        renderCart();
        updateTotals();
    }
    
    // Filter products
    function filterProducts() {
        const searchTerm = productSearch.value.toLowerCase();
        const selectedCategory = document.querySelector('.category-pill.active').dataset.category;
        
        let filteredProducts = products;
        
        // Filter by category
        if (selectedCategory !== 'all') {
            filteredProducts = filteredProducts.filter(product => 
                product.category_id == selectedCategory
            );
        }
        
        // Filter by search term
        if (searchTerm) {
            filteredProducts = filteredProducts.filter(product => 
                product.name.toLowerCase().includes(searchTerm)
            );
        }
        
        renderProducts(filteredProducts);
    }
    
    // Clear search
    function clearSearch() {
        productSearch.value = '';
        filterProducts();
    }
    
    // Show checkout modal
    function showCheckoutModal() {
        const modal = new bootstrap.Modal(document.getElementById('checkout-modal'));
        modal.show();
    }
    
    // Complete sale
    function completeSale() {
        const saleData = {
            customer_name: document.getElementById('customer-name').value,
            customer_phone: document.getElementById('customer-phone').value,
            customer_email: document.getElementById('customer-email').value,
            payment_method: document.getElementById('payment-method').value,
            notes: document.getElementById('notes').value,
            subtotal: subtotal,
            discount: discount,
            tax: tax,
            items: cart.map(item => ({
                product_id: item.id,
                quantity: item.quantity,
                price: item.price,
                discount: 0
            }))
        };
        
        // Send data to server
        fetch('{% url "sales:new_sale" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(saleData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hide modal
                bootstrap.Modal.getInstance(document.getElementById('checkout-modal')).hide();
                
                // Clear cart
                clearCart();
                
                // Show success message
                alert(`{% translate "Sale completed successfully! Invoice" %} #${data.invoice_number}`);
                
                // Redirect to sale detail
                window.location.href = `{% url "sales:sale_detail" 0 %}`.replace('0', data.sale_id);
            } else {
                alert(data.message || '{% translate "An error occurred" %}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% translate "An error occurred" %}');
        });
    }
    
    // Get CSRF token from cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}
