{% extends 'base.html' %}

{% block title %}Dashboard - POS System{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block content %}
<div class="row">
    <!-- Stats Cards -->
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">Total Sales</h6>
                        <h2 class="mb-0">{{ total_sales }}</h2>
                    </div>
                    <i class="fas fa-shopping-cart fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{% url 'sales:sales_list' %}" class="text-white">View Details <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">Total Revenue</h6>
                        <h2 class="mb-0">${{ total_revenue|floatformat:2 }}</h2>
                    </div>
                    <i class="fas fa-dollar-sign fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{% url 'reports:sales_report' %}" class="text-white">View Report <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">Total Products</h6>
                        <h2 class="mb-0">{{ total_products }}</h2>
                    </div>
                    <i class="fas fa-box fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{% url 'products:product_list' %}" class="text-white">View Products <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">Low Stock Items</h6>
                        <h2 class="mb-0">{{ low_stock_items.count }}</h2>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{% url 'inventory:low_stock' %}" class="text-white">View Details <i class="fas fa-arrow-right ms-1"></i></a>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Recent Sales -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Sales</h5>
                <a href="{% url 'sales:sales_list' %}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Invoice</th>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in recent_sales %}
                            <tr>
                                <td><a href="{% url 'sales:sale_detail' sale.id %}">{{ sale.invoice_number }}</a></td>
                                <td>{{ sale.created_at|date:"M d, Y" }}</td>
                                <td>{{ sale.customer_name|default:"Walk-in Customer" }}</td>
                                <td>${{ sale.total|floatformat:2 }}</td>
                                <td>
                                    {% if sale.status == 'completed' %}
                                    <span class="badge bg-success">Completed</span>
                                    {% elif sale.status == 'refunded' %}
                                    <span class="badge bg-danger">Refunded</span>
                                    {% elif sale.status == 'partially_refunded' %}
                                    <span class="badge bg-warning">Partially Refunded</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ sale.get_status_display }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">No sales found</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Low Stock Items -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Low Stock Items</h5>
                <a href="{% url 'inventory:low_stock' %}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Current Stock</th>
                                <th>Reorder Level</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in low_stock_items %}
                            <tr>
                                <td>{{ item.product.name }}</td>
                                <td>
                                    {% if item.quantity == 0 %}
                                    <span class="badge bg-danger">Out of Stock</span>
                                    {% else %}
                                    <span class="badge bg-warning">{{ item.quantity }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ item.reorder_level }}</td>
                                <td>
                                    <a href="{% url 'inventory:inventory_restock' item.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus-circle"></i> Restock
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">No low stock items found</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Quick Actions -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'sales:new_sale' %}" class="btn btn-primary btn-lg w-100 d-flex align-items-center justify-content-center">
                            <i class="fas fa-shopping-cart me-2"></i> New Sale
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'products:product_add' %}" class="btn btn-success btn-lg w-100 d-flex align-items-center justify-content-center">
                            <i class="fas fa-plus-circle me-2"></i> Add Product
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'inventory:inventory_list' %}" class="btn btn-info btn-lg w-100 d-flex align-items-center justify-content-center text-white">
                            <i class="fas fa-warehouse me-2"></i> Inventory
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'reports:reports_dashboard' %}" class="btn btn-warning btn-lg w-100 d-flex align-items-center justify-content-center text-white">
                            <i class="fas fa-chart-bar me-2"></i> Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
