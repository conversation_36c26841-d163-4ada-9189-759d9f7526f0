{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Sale" %} #{{ sale.invoice_number }} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Sale Details" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'sales:sales_list' %}">{% translate "Sales" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% translate "Invoice" %} #{{ sale.invoice_number }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group">
            <a href="{% url 'sales:generate_invoice' sale.id %}" class="btn btn-primary">
                <i class="fas fa-file-invoice {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Print Invoice" %}
            </a>
            {% if sale.status == 'completed' %}
            <a href="{% url 'sales:refund_sale' sale.id %}" class="btn btn-warning">
                <i class="fas fa-undo {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Process Refund" %}
            </a>
            {% endif %}
            <a href="{% url 'sales:sales_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Back to Sales" %}
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{% translate "Items Purchased" %}</h5>
                <span class="badge bg-primary">{{ items|length }} {% translate "items" %}</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>{% translate "Product" %}</th>
                                <th class="text-center">{% translate "Quantity" %}</th>
                                <th class="text-end">{% translate "Unit Price" %}</th>
                                <th class="text-end">{% translate "Discount" %}</th>
                                <th class="text-end">{% translate "Total" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if item.product.image %}
                                        <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: contain;">
                                        {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                            <i class="fas fa-box text-muted"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <div>{{ item.product.name }}</div>
                                            <small class="text-muted">{{ item.product.sku }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">{{ item.quantity }}</td>
                                <td class="text-end">${{ item.price|floatformat:2 }}</td>
                                <td class="text-end">${{ item.discount|floatformat:2 }}</td>
                                <td class="text-end">${{ item.total|floatformat:2 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <td colspan="4" class="text-end fw-bold">{% translate "Subtotal" %}:</td>
                                <td class="text-end">${{ sale.subtotal|floatformat:2 }}</td>
                            </tr>
                            {% if sale.discount > 0 %}
                            <tr>
                                <td colspan="4" class="text-end fw-bold">{% translate "Discount" %}:</td>
                                <td class="text-end">-${{ sale.discount|floatformat:2 }}</td>
                            </tr>
                            {% endif %}
                            {% if sale.tax > 0 %}
                            <tr>
                                <td colspan="4" class="text-end fw-bold">{% translate "Tax" %}:</td>
                                <td class="text-end">${{ sale.tax|floatformat:2 }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td colspan="4" class="text-end fw-bold">{% translate "Total" %}:</td>
                                <td class="text-end fw-bold">${{ sale.total|floatformat:2 }}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        
        {% if sale.notes %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Notes" %}</h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ sale.notes }}</p>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Sale Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="mb-3 text-center">
                    <span class="badge bg-lg 
                        {% if sale.status == 'completed' %}bg-success
                        {% elif sale.status == 'refunded' %}bg-danger
                        {% elif sale.status == 'partially_refunded' %}bg-warning
                        {% else %}bg-secondary{% endif %} p-2">
                        {{ sale.get_status_display }}
                    </span>
                </div>
                
                <div class="row mb-2">
                    <div class="col-5">{% translate "Invoice #" %}:</div>
                    <div class="col-7 text-end">{{ sale.invoice_number }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-5">{% translate "Date" %}:</div>
                    <div class="col-7 text-end">{{ sale.created_at|date:"M d, Y H:i" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-5">{% translate "Payment" %}:</div>
                    <div class="col-7 text-end">{{ sale.get_payment_method_display }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-5">{% translate "Cashier" %}:</div>
                    <div class="col-7 text-end">{{ sale.cashier.user.username }}</div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Customer Information" %}</h5>
            </div>
            <div class="card-body">
                {% if sale.customer_name or sale.customer_phone or sale.customer_email %}
                <div class="row mb-2">
                    <div class="col-4">{% translate "Name" %}:</div>
                    <div class="col-8 text-end">{{ sale.customer_name|default:"—" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-4">{% translate "Phone" %}:</div>
                    <div class="col-8 text-end">{{ sale.customer_phone|default:"—" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-4">{% translate "Email" %}:</div>
                    <div class="col-8 text-end">{{ sale.customer_email|default:"—" }}</div>
                </div>
                {% else %}
                <p class="text-center text-muted mb-0">{% translate "Walk-in Customer" %}</p>
                {% endif %}
            </div>
        </div>
        
        {% if sale.status == 'refunded' or sale.status == 'partially_refunded' %}
        <div class="card mb-4 border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">{% translate "Refund Information" %}</h5>
            </div>
            <div class="card-body">
                <p>
                    {% if sale.status == 'refunded' %}
                    {% translate "This sale has been fully refunded." %}
                    {% else %}
                    {% translate "This sale has been partially refunded." %}
                    {% endif %}
                </p>
                
                <!-- Here you would show refund details if you track them -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                    {% translate "Check inventory records for returned items." %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
