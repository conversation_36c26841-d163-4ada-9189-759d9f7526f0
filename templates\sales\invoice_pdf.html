<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ sale.invoice_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .invoice-box {
            max-width: 800px;
            margin: auto;
            padding: 30px;
            border: 1px solid #eee;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .company-info {
            text-align: left;
        }
        .invoice-info {
            text-align: right;
        }
        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
        }
        .customer-info {
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        table th {
            background-color: #f8f8f8;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .totals {
            width: 50%;
            float: right;
        }
        .totals table {
            width: 100%;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #777;
            font-size: 11px;
        }
        .status-stamp {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-30deg);
            font-size: 60px;
            color: rgba(255, 0, 0, 0.3);
            font-weight: bold;
            border: 10px solid rgba(255, 0, 0, 0.3);
            padding: 10px 20px;
            border-radius: 10px;
            z-index: 100;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="invoice-box">
        {% if sale.status == 'refunded' %}
        <div class="status-stamp">REFUNDED</div>
        {% elif sale.status == 'partially_refunded' %}
        <div class="status-stamp">PARTIALLY REFUNDED</div>
        {% elif sale.status == 'cancelled' %}
        <div class="status-stamp">CANCELLED</div>
        {% endif %}
        
        <div class="invoice-header">
            <div class="company-info">
                <h2>{{ company_name|default:"POS System" }}</h2>
                <p>{{ company_address|default:"123 Main Street" }}</p>
                <p>{{ company_city|default:"Anytown" }}, {{ company_state|default:"ST" }} {{ company_zip|default:"12345" }}</p>
                <p>{{ company_phone|default:"(*************" }}</p>
                <p>{{ company_email|default:"<EMAIL>" }}</p>
            </div>
            <div class="invoice-info">
                <h3>INVOICE</h3>
                <p><strong>Invoice #:</strong> {{ sale.invoice_number }}</p>
                <p><strong>Date:</strong> {{ sale.created_at|date:"M d, Y H:i" }}</p>
                <p><strong>Payment Method:</strong> {{ sale.get_payment_method_display }}</p>
                <p><strong>Status:</strong> {{ sale.get_status_display }}</p>
            </div>
        </div>
        
        <div class="invoice-title">INVOICE</div>
        
        <div class="customer-info">
            <h3>Bill To:</h3>
            <p>{{ sale.customer_name|default:"Walk-in Customer" }}</p>
            {% if sale.customer_phone %}
            <p>{{ sale.customer_phone }}</p>
            {% endif %}
            {% if sale.customer_email %}
            <p>{{ sale.customer_email }}</p>
            {% endif %}
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Item</th>
                    <th class="text-center">Quantity</th>
                    <th class="text-right">Unit Price</th>
                    <th class="text-right">Discount</th>
                    <th class="text-right">Amount</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>
                        {{ item.product.name }}<br>
                        <small>{{ item.product.sku }}</small>
                    </td>
                    <td class="text-center">{{ item.quantity }}</td>
                    <td class="text-right">${{ item.price|floatformat:2 }}</td>
                    <td class="text-right">${{ item.discount|floatformat:2 }}</td>
                    <td class="text-right">${{ item.total|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="totals">
            <table>
                <tr>
                    <td><strong>Subtotal:</strong></td>
                    <td class="text-right">${{ sale.subtotal|floatformat:2 }}</td>
                </tr>
                {% if sale.discount > 0 %}
                <tr>
                    <td><strong>Discount:</strong></td>
                    <td class="text-right">-${{ sale.discount|floatformat:2 }}</td>
                </tr>
                {% endif %}
                {% if sale.tax > 0 %}
                <tr>
                    <td><strong>Tax:</strong></td>
                    <td class="text-right">${{ sale.tax|floatformat:2 }}</td>
                </tr>
                {% endif %}
                <tr>
                    <td><strong>Total:</strong></td>
                    <td class="text-right"><strong>${{ sale.total|floatformat:2 }}</strong></td>
                </tr>
            </table>
        </div>
        
        <div style="clear: both;"></div>
        
        {% if sale.notes %}
        <div style="margin-top: 20px;">
            <h3>Notes:</h3>
            <p>{{ sale.notes }}</p>
        </div>
        {% endif %}
        
        <div class="footer">
            <p>Thank you for your business!</p>
            <p>This invoice was generated by POS System on {{ sale.created_at|date:"M d, Y H:i" }}</p>
        </div>
    </div>
</body>
</html>
