{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Low Stock Items" %} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Low Stock Items" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
            {% translate "These items are below their reorder level and need to be restocked." %}
        </div>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'inventory:inventory_list' %}" class="btn btn-primary">
            <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Back to Inventory" %}
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{% translate "Low Stock Items" %}</h5>
        <span class="badge bg-warning text-dark">{{ low_stock_items|length }} {% translate "items" %}</span>
    </div>
    <div class="card-body">
        {% if low_stock_items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% translate "Product" %}</th>
                        <th>{% translate "SKU" %}</th>
                        <th>{% translate "Category" %}</th>
                        <th>{% translate "Current Stock" %}</th>
                        <th>{% translate "Reorder Level" %}</th>
                        <th>{% translate "Status" %}</th>
                        <th>{% translate "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in low_stock_items %}
                    <tr>
                        <td>
                            <a href="{% url 'inventory:inventory_detail' item.id %}">{{ item.product.name }}</a>
                        </td>
                        <td>{{ item.product.sku }}</td>
                        <td>{{ item.product.category.name|default:"—" }}</td>
                        <td>
                            {% if item.quantity == 0 %}
                            <span class="badge bg-danger">0</span>
                            {% else %}
                            <span class="badge bg-warning">{{ item.quantity }}</span>
                            {% endif %}
                        </td>
                        <td>{{ item.reorder_level }}</td>
                        <td>
                            {% if item.quantity == 0 %}
                            <span class="badge bg-danger">{% translate "Out of Stock" %}</span>
                            {% else %}
                            <span class="badge bg-warning">{% translate "Low Stock" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'inventory:inventory_restock' item.id %}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus {% if LANGUAGE_CODE == 'ar' %}ms-1{% else %}me-1{% endif %}"></i> {% translate "Restock" %}
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-success">
            <i class="fas fa-check-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
            {% translate "Great! All items are above their reorder levels." %}
        </div>
        {% endif %}
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Reorder Recommendations" %}</h5>
            </div>
            <div class="card-body">
                {% if low_stock_items %}
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>{% translate "Product" %}</th>
                                <th>{% translate "Current Stock" %}</th>
                                <th>{% translate "Reorder Level" %}</th>
                                <th>{% translate "Recommended Order" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in low_stock_items %}
                            <tr>
                                <td>{{ item.product.name }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>{{ item.reorder_level }}</td>
                                <td>{{ item.reorder_level|add:5 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="mb-0">{% translate "No reorder recommendations at this time." %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
