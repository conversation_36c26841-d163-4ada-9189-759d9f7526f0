{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Inventory" %} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Inventory" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <form method="get" class="d-flex">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="{% translate 'Search inventory...' %}" value="{{ search_query }}">
                <button class="btn btn-primary" type="submit">
                    <i class="fas fa-search"></i> {% translate "Search" %}
                </button>
            </div>
        </form>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'inventory:inventory_add' %}" class="btn btn-success">
            <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Add Inventory" %}
        </a>
        <a href="{% url 'inventory:low_stock' %}" class="btn btn-warning text-white">
            <i class="fas fa-exclamation-triangle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Low Stock" %}
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{% translate "Inventory List" %}</h5>
        <div>
            <div class="form-check form-switch d-inline-block me-3">
                <input class="form-check-input" type="checkbox" id="lowStockFilter" {% if low_stock %}checked{% endif %} onchange="window.location.href='{% url 'inventory:inventory_list' %}{% if not low_stock %}?low_stock=1{% endif %}'">
                <label class="form-check-label" for="lowStockFilter">{% translate "Show Low Stock Only" %}</label>
            </div>
            <span class="badge bg-primary">{{ inventory_items|length }} {% translate "items" %}</span>
        </div>
    </div>
    <div class="card-body">
        {% if inventory_items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% translate "Product" %}</th>
                        <th>{% translate "SKU" %}</th>
                        <th>{% translate "Category" %}</th>
                        <th>{% translate "Quantity" %}</th>
                        <th>{% translate "Reorder Level" %}</th>
                        <th>{% translate "Last Restock" %}</th>
                        <th>{% translate "Status" %}</th>
                        <th>{% translate "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in inventory_items %}
                    <tr>
                        <td>
                            <a href="{% url 'inventory:inventory_detail' item.id %}">{{ item.product.name }}</a>
                        </td>
                        <td>{{ item.product.sku }}</td>
                        <td>{{ item.product.category.name|default:"—" }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.reorder_level }}</td>
                        <td>
                            {% if item.last_restock_date %}
                            {{ item.last_restock_date|date:"M d, Y" }}
                            {% else %}
                            —
                            {% endif %}
                        </td>
                        <td>
                            {% if item.quantity == 0 %}
                            <span class="badge bg-danger">{% translate "Out of Stock" %}</span>
                            {% elif item.quantity <= item.reorder_level %}
                            <span class="badge bg-warning">{% translate "Low Stock" %}</span>
                            {% else %}
                            <span class="badge bg-success">{% translate "In Stock" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'inventory:inventory_edit' item.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'inventory:inventory_detail' item.id %}" class="btn btn-sm btn-info text-white">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'inventory:inventory_restock' item.id %}" class="btn btn-sm btn-success">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            {% if search_query %}
            {% translate "No inventory items found matching your search criteria." %}
            {% else %}
            {% translate "No inventory items found. Add your first inventory item using the 'Add Inventory' button." %}
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
