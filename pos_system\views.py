from django.shortcuts import render
from django.utils.translation import gettext as _
from django.http import HttpResponse

def test_i18n(request):
    """
    A simple view to test internationalization
    """
    output = [
        f'<h1>{_("POS System")}</h1>',
        f'<p>{_("Welcome to our Point of Sale System")}</p>',
        '<hr>',
        f'<p>{_("Current language")}: {request.LANGUAGE_CODE}</p>',
        '<h2>Language Switcher:</h2>',
        '<form action="/i18n/setlang/" method="post">',
        f'<input type="hidden" name="csrfmiddlewaretoken" value="{request.META.get("CSRF_COOKIE", "")}">',
        '<input name="next" type="hidden" value="/">',
        '<select name="language" onchange="this.form.submit()">',
        f'<option value="en" {"selected" if request.LANGUAGE_CODE == "en" else ""}>English</option>',
        f'<option value="fr" {"selected" if request.LANGUAGE_CODE == "fr" else ""}>Français</option>',
        f'<option value="ar" {"selected" if request.LANGUAGE_CODE == "ar" else ""}>العربية</option>',
        '</select>',
        '</form>',
        '<hr>',
        '<a href="/">Back to Dashboard</a>'
    ]
    return HttpResponse('<br>'.join(output))
