from django.contrib import admin
from .models import Sale, SaleItem

class SaleItemInline(admin.TabularInline):
    model = SaleItem
    extra = 0
    readonly_fields = ('total',)

@admin.register(Sale)
class SaleAdmin(admin.ModelAdmin):
    list_display = ('invoice_number', 'customer_name', 'total', 'payment_method', 'status', 'created_at')
    list_filter = ('status', 'payment_method', 'created_at')
    search_fields = ('invoice_number', 'customer_name', 'customer_email')
    readonly_fields = ('invoice_number', 'created_at', 'updated_at', 'total')
    inlines = [SaleItemInline]
    fieldsets = (
        ('Customer Information', {
            'fields': ('customer_name', 'customer_phone', 'customer_email')
        }),
        ('Sale Information', {
            'fields': ('invoice_number', 'cashier', 'payment_method', 'status')
        }),
        ('Financial Details', {
            'fields': ('subtotal', 'discount', 'tax', 'total')
        }),
        ('Additional Information', {
            'fields': ('notes', 'created_at', 'updated_at')
        }),
    )

@admin.register(SaleItem)
class SaleItemAdmin(admin.ModelAdmin):
    list_display = ('sale', 'product', 'quantity', 'price', 'total')
    list_filter = ('sale__created_at',)
    search_fields = ('sale__invoice_number', 'product__name')
    readonly_fields = ('total',)
