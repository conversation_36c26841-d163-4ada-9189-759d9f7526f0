{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Delete" %} {{ product.name }} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Delete Product" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'products:product_list' %}">{% translate "Products" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'products:product_detail' product.id %}">{{ product.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% translate "Delete" %}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'products:product_detail' product.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Back to Product" %}
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">
            <i class="fas fa-exclamation-triangle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
            {% translate "Confirm Product Deletion" %}
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <h5 class="alert-heading">{% translate "Warning!" %}</h5>
            <p>{% translate "You are about to delete the following product:" %} <strong>{{ product.name }}</strong></p>
            <p>{% translate "This action cannot be undone. All data associated with this product will be permanently removed from the system." %}</p>
            
            {% if has_sales %}
            <hr>
            <p class="mb-0">
                <i class="fas fa-exclamation-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                {% translate "This product has sales records associated with it. Deleting it may affect historical sales data." %}
            </p>
            {% endif %}
        </div>
        
        <div class="row mb-4">
            <div class="col-md-3">
                {% if product.image %}
                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid img-thumbnail">
                {% else %}
                <div class="bg-light d-flex align-items-center justify-content-center" style="height: 150px;">
                    <i class="fas fa-image fa-4x text-muted"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <h4>{{ product.name }}</h4>
                <p class="text-muted">{{ product.sku }}</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>{% translate "Category" %}:</strong> {{ product.category.name|default:"—" }}</p>
                        <p><strong>{% translate "Price" %}:</strong> ${{ product.price|floatformat:2 }}</p>
                    </div>
                    <div class="col-md-6">
                        {% if inventory %}
                        <p><strong>{% translate "Current Stock" %}:</strong> {{ inventory.quantity }}</p>
                        {% endif %}
                        <p>
                            <strong>{% translate "Status" %}:</strong>
                            {% if product.is_active %}
                            <span class="badge bg-success">{% translate "Active" %}</span>
                            {% else %}
                            <span class="badge bg-danger">{% translate "Inactive" %}</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
                <a href="{% url 'products:product_detail' product.id %}" class="btn btn-secondary">
                    <i class="fas fa-times {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Cancel" %}
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Permanently Delete Product" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
