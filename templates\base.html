<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}POS System{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --success-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
        }
        
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background-color: var(--secondary-color);
            color: white;
            min-height: 100vh;
            padding: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.8rem 1rem;
            border-left: 3px solid transparent;
        }
        
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left: 3px solid var(--primary-color);
        }
        
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left: 3px solid var(--primary-color);
        }
        
        .sidebar .nav-link i {
            margin-right: 10px;
        }
        
        .content {
            padding: 20px;
        }
        
        .navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: bold;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            padding: 1rem;
            text-align: center;
            background-color: rgba(0, 0, 0, 0.2);
        }
        
        .logo i {
            margin-right: 10px;
        }
        
        .user-info {
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 1rem;
        }
        
        .user-info img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <div class="logo">
                    <i class="fas fa-cash-register"></i> POS System
                </div>
                <div class="user-info">
                    {% if user.is_authenticated %}
                        {% if user.profile.profile_image %}
                            <img src="{{ user.profile.profile_image.url }}" alt="{{ user.username }}">
                        {% else %}
                            <i class="fas fa-user-circle fa-2x me-2"></i>
                        {% endif %}
                        <span>{{ user.username }}</span>
                    {% endif %}
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/' %}active{% endif %}" href="{% url 'accounts:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/products/' in request.path %}active{% endif %}" href="{% url 'products:product_list' %}">
                            <i class="fas fa-box"></i> Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/inventory/' in request.path %}active{% endif %}" href="{% url 'inventory:inventory_list' %}">
                            <i class="fas fa-warehouse"></i> Inventory
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/sales/new/' in request.path %}active{% endif %}" href="{% url 'sales:new_sale' %}">
                            <i class="fas fa-shopping-cart"></i> New Sale
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/sales/' in request.path and not '/sales/new/' in request.path %}active{% endif %}" href="{% url 'sales:sales_list' %}">
                            <i class="fas fa-receipt"></i> Sales
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/reports/' in request.path %}active{% endif %}" href="{% url 'reports:reports_dashboard' %}">
                            <i class="fas fa-chart-bar"></i> Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/accounts/profile/' in request.path %}active{% endif %}" href="{% url 'accounts:profile' %}">
                            <i class="fas fa-user-cog"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:logout' %}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 content">
                <!-- Navbar -->
                <nav class="navbar navbar-expand-lg navbar-light mb-4">
                    <div class="container-fluid">
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarNav">
                            <ul class="navbar-nav me-auto">
                                <li class="nav-item">
                                    <h4 class="mb-0">{% block page_title %}Dashboard{% endblock %}</h4>
                                </li>
                            </ul>
                            <div class="d-flex">
                                <span class="navbar-text me-3">
                                    <i class="far fa-clock"></i> {% now "F j, Y, g:i a" %}
                                </span>
                            </div>
                        </div>
                    </div>
                </nav>
                
                <!-- Messages -->
                {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                
                <!-- Main Content -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script>
        // Auto-dismiss alerts after 5 seconds
        $(document).ready(function() {
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
