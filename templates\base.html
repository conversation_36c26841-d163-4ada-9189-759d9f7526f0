{% load i18n %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}"{% if LANGUAGE_CODE == 'ar' %} dir="rtl"{% endif %}>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}POS System{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    {% if LANGUAGE_CODE == 'ar' %}
    <!-- Bootstrap RTL CSS for Arabic -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.rtl.min.css">
    {% endif %}
    <style>
        :root {
            /* Modern Color Palette */
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #a5b4fc;
            --secondary-color: #1e293b;
            --secondary-light: #334155;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #06b6d4;

            /* Background Colors */
            --bg-primary: #f8fafc;
            --bg-secondary: #f1f5f9;
            --bg-card: #ffffff;
            --bg-sidebar: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

            /* Text Colors */
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-light: #94a3b8;
            --text-white: #ffffff;

            /* Shadow */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;

            /* Transitions */
            --transition-fast: 0.15s ease-in-out;
            --transition-normal: 0.3s ease-in-out;
            --transition-slow: 0.5s ease-in-out;
        }

        {% if LANGUAGE_CODE == 'ar' %}
        /* RTL specific styles */
        html[lang="ar"] {
            direction: rtl;
        }

        html[lang="ar"] body {
            direction: rtl;
            text-align: right;
        }

        html[lang="ar"] .sidebar .nav-link {
            border-left: none;
            border-right: 3px solid transparent;
        }

        html[lang="ar"] .sidebar .nav-link:hover,
        html[lang="ar"] .sidebar .nav-link.active {
            border-left: none;
            border-right: 3px solid var(--primary-light);
            transform: translateX(-5px);
        }

        html[lang="ar"] .sidebar .nav-link i {
            margin-right: 0;
            margin-left: 12px;
        }

        html[lang="ar"] .logo i {
            margin-right: 0;
            margin-left: 0.5rem;
        }

        html[lang="ar"] .user-info img {
            margin-right: 0;
            margin-left: 12px;
        }

        /* Fix Bootstrap RTL issues */
        html[lang="ar"] .navbar-nav {
            margin-left: auto !important;
            margin-right: 0 !important;
        }

        html[lang="ar"] .dropdown-menu-end {
            left: 0 !important;
            right: auto !important;
        }

        html[lang="ar"] .me-auto {
            margin-left: auto !important;
            margin-right: 0 !important;
        }

        html[lang="ar"] .ms-2 {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
        }

        html[lang="ar"] .me-2 {
            margin-right: 0.5rem !important;
            margin-left: 0 !important;
        }

        html[lang="ar"] .text-end {
            text-align: left !important;
        }

        html[lang="ar"] .text-start {
            text-align: right !important;
        }

        /* Fix sidebar content alignment for Arabic */
        html[lang="ar"] .sidebar {
            text-align: right;
        }

        html[lang="ar"] .sidebar .logo {
            text-align: center;
        }

        html[lang="ar"] .sidebar .user-info {
            text-align: center;
        }

        html[lang="ar"] .sidebar .nav-link {
            text-align: right;
            flex-direction: row-reverse;
            justify-content: space-between;
        }

        html[lang="ar"] .sidebar .nav-link i {
            margin-left: 0;
            margin-right: 0;
            order: 2;
        }

        html[lang="ar"] .sidebar .nav-link span {
            text-align: right;
            order: 1;
            flex: 1;
        }

        /* Fix logo icon for Arabic */
        html[lang="ar"] .logo i {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        /* Fix user info for Arabic */
        html[lang="ar"] .user-info i {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        /* Fix navbar for Arabic */
        html[lang="ar"] .navbar-nav {
            flex-direction: row-reverse;
        }

        html[lang="ar"] .navbar .d-flex {
            flex-direction: row-reverse;
        }

        html[lang="ar"] .navbar-text {
            margin-left: 0 !important;
            margin-right: 1rem !important;
        }

        /* Fix button margins for Arabic */
        html[lang="ar"] .btn .fas,
        html[lang="ar"] .btn .far {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        /* Fix card headers for Arabic */
        html[lang="ar"] .card-header {
            text-align: right;
        }

        /* Fix table headers for Arabic */
        html[lang="ar"] .table th {
            text-align: right;
        }

        /* Fix form labels for Arabic */
        html[lang="ar"] .form-label {
            text-align: right;
        }

        /* Fix breadcrumb for Arabic */
        html[lang="ar"] .breadcrumb {
            flex-direction: row-reverse;
        }

        html[lang="ar"] .breadcrumb-item + .breadcrumb-item::before {
            float: right;
            padding-right: 0;
            padding-left: 0.5rem;
            content: "‹";
        }
        {% endif %}

        /* Base Styles */
        * {
            box-sizing: border-box;
        }

        body {
            {% if LANGUAGE_CODE == 'ar' %}
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            {% else %}
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            {% endif %}
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-light);
            border-radius: var(--radius-md);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* Sidebar Styles */
        .sidebar {
            background: var(--bg-sidebar);
            color: var(--text-white);
            min-height: 100vh;
            padding: 0;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-xl);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 1rem 1.5rem;
            border-left: 3px solid transparent;
            transition: all var(--transition-normal);
            position: relative;
            display: flex;
            align-items: center;
            text-decoration: none;
            font-weight: 500;
            margin: 0.25rem 0;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 0;
            background: rgba(255, 255, 255, 0.1);
            transition: width var(--transition-normal);
            z-index: 1;
        }

        .sidebar .nav-link:hover::before {
            width: 100%;
        }

        .sidebar .nav-link:hover {
            color: var(--text-white);
            background-color: rgba(255, 255, 255, 0.15);
            border-left: 3px solid var(--primary-light);
            transform: translateX(5px);
            box-shadow: var(--shadow-md);
        }

        .sidebar .nav-link.active {
            color: var(--text-white);
            background-color: rgba(255, 255, 255, 0.2);
            border-left: 3px solid var(--primary-light);
            font-weight: 600;
            box-shadow: var(--shadow-md);
        }

        .sidebar .nav-link i {
            margin-right: 12px;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
            transition: transform var(--transition-normal);
            z-index: 2;
            position: relative;
        }

        .sidebar .nav-link:hover i {
            transform: scale(1.1);
        }

        .sidebar .nav-link span {
            z-index: 2;
            position: relative;
        }

        /* Content Area */
        .content {
            padding: 2rem;
            background: var(--bg-primary);
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background: var(--bg-card);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            border-radius: var(--radius-lg);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .navbar h4 {
            color: var(--text-primary);
            font-weight: 600;
            margin: 0;
        }

        .navbar-text {
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Card Styles */
        .card {
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            margin-bottom: 2rem;
            border: none;
            background: var(--bg-card);
            transition: all var(--transition-normal);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border-bottom: none;
            font-weight: 600;
            padding: 1.5rem;
            position: relative;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
        }

        .card-body {
            padding: 2rem;
        }

        /* Button Styles */
        .btn {
            border-radius: var(--radius-md);
            font-weight: 500;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left var(--transition-slow);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            border: none;
            box-shadow: var(--shadow-md);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
            border: none;
            box-shadow: var(--shadow-md);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
            border: none;
            box-shadow: var(--shadow-md);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Logo Styles */
        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-white);
            padding: 2rem 1.5rem;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }

        .logo i {
            background: linear-gradient(45deg, var(--primary-light), var(--text-white));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2rem;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* User Info Styles */
        .user-info {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .user-info img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 12px;
            border: 3px solid var(--primary-light);
            transition: all var(--transition-normal);
        }

        .user-info img:hover {
            transform: scale(1.1);
            border-color: var(--text-white);
        }

        .user-info span {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--text-white);
        }

        /* Additional Styles */
        .form-control {
            border-radius: var(--radius-md);
            border: 2px solid var(--bg-secondary);
            transition: all var(--transition-normal);
            padding: 0.75rem 1rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }

        .table {
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            border: none;
            font-weight: 600;
            padding: 1rem;
        }

        .table tbody tr {
            transition: all var(--transition-fast);
        }

        .table tbody tr:hover {
            background-color: var(--bg-secondary);
            transform: scale(1.01);
        }

        .badge {
            border-radius: var(--radius-md);
            font-weight: 500;
            padding: 0.5rem 0.75rem;
        }

        .alert {
            border-radius: var(--radius-lg);
            border: none;
            box-shadow: var(--shadow-md);
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            {% if LANGUAGE_CODE == 'ar' %}
            <!-- Main Content (Arabic - Left side) -->
            <div class="col-md-10 content order-1">
            {% else %}
            <!-- Sidebar (Non-Arabic - Left side) -->
            <div class="col-md-2 sidebar">
                <div class="logo">
                    <i class="fas fa-cash-register {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "POS System" %}
                </div>
                <div class="user-info">
                    {% if user.is_authenticated %}
                        {% if user.profile.profile_image %}
                            <img src="{{ user.profile.profile_image.url }}" alt="{{ user.username }}">
                        {% else %}
                            <i class="fas fa-user-circle fa-2x {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                        {% endif %}
                        <span>{{ user.username }}</span>
                    {% endif %}
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/' %}active{% endif %}" href="{% url 'accounts:dashboard' %}">
                            <i class="fas fa-tachometer-alt {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                            <span>{% translate "Dashboard" %}</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/products/' in request.path %}active{% endif %}" href="{% url 'products:product_list' %}">
                            <i class="fas fa-box {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                            <span>{% translate "Products" %}</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/inventory/' in request.path %}active{% endif %}" href="{% url 'inventory:inventory_list' %}">
                            <i class="fas fa-warehouse {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                            <span>{% translate "Inventory" %}</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/sales/new/' in request.path %}active{% endif %}" href="{% url 'sales:new_sale' %}">
                            <i class="fas fa-shopping-cart {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                            <span>{% translate "New Sale" %}</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/sales/' in request.path and not '/sales/new/' in request.path %}active{% endif %}" href="{% url 'sales:sales_list' %}">
                            <i class="fas fa-receipt {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                            <span>{% translate "Sales" %}</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/reports/' in request.path %}active{% endif %}" href="{% url 'reports:reports_dashboard' %}">
                            <i class="fas fa-chart-bar {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                            <span>{% translate "Reports" %}</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/accounts/profile/' in request.path %}active{% endif %}" href="{% url 'accounts:profile' %}">
                            <i class="fas fa-user-cog {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                            <span>{% translate "Profile" %}</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:logout' %}">
                            <i class="fas fa-sign-out-alt {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                            <span>{% translate "Logout" %}</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content (Non-Arabic - Right side) -->
            <div class="col-md-10 content">
            {% endif %}
                <!-- Navbar -->
                <nav class="navbar navbar-expand-lg navbar-light mb-4">
                    <div class="container-fluid">
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarNav">
                            <ul class="navbar-nav me-auto">
                                <li class="nav-item">
                                    <h4 class="mb-0">{% block page_title %}Dashboard{% endblock %}</h4>
                                </li>
                            </ul>
                            <div class="d-flex">
                                <span class="navbar-text me-3">
                                    <i class="far fa-clock"></i> {% now "F j, Y, g:i a" %}
                                </span>
                                <!-- Language Switcher -->
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        {% if LANGUAGE_CODE == 'ar' %}
                                            <i class="fas fa-language"></i> العربية
                                        {% elif LANGUAGE_CODE == 'fr' %}
                                            <i class="fas fa-language"></i> Français
                                        {% else %}
                                            <i class="fas fa-language"></i> English
                                        {% endif %}
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                                        <form action="{% url 'set_language' %}" method="post" id="language-form">
                                            {% csrf_token %}
                                            <input name="next" type="hidden" value="{{ request.path }}">
                                            <li>
                                                <button type="submit" name="language" value="en" class="dropdown-item {% if LANGUAGE_CODE == 'en' %}active{% endif %}">
                                                    English
                                                </button>
                                            </li>
                                            <li>
                                                <button type="submit" name="language" value="fr" class="dropdown-item {% if LANGUAGE_CODE == 'fr' %}active{% endif %}">
                                                    Français
                                                </button>
                                            </li>
                                            <li>
                                                <button type="submit" name="language" value="ar" class="dropdown-item {% if LANGUAGE_CODE == 'ar' %}active{% endif %}">
                                                    العربية
                                                </button>
                                            </li>
                                        </form>
                                    </ul>
                                </div>
                                <!-- Test i18n link -->
                                <a href="{% url 'test_i18n' %}" class="btn btn-sm btn-outline-info ms-2">
                                    <i class="fas fa-globe"></i> {% translate "Test i18n" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- Messages -->
                {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Main Content -->
                {% block content %}{% endblock %}
            </div>

            {% if LANGUAGE_CODE == 'ar' %}
            <!-- Sidebar (Arabic - Right side) -->
            <div class="col-md-2 sidebar order-2">
                <div class="logo">
                    <i class="fas fa-cash-register me-2"></i> {% translate "POS System" %}
                </div>
                <div class="user-info">
                    {% if user.is_authenticated %}
                        {% if user.profile.profile_image %}
                            <img src="{{ user.profile.profile_image.url }}" alt="{{ user.username }}">
                        {% else %}
                            <i class="fas fa-user-circle fa-2x me-2"></i>
                        {% endif %}
                        <span>{{ user.username }}</span>
                    {% endif %}
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/' %}active{% endif %}" href="{% url 'accounts:dashboard' %}">
                            <span>{% translate "Dashboard" %}</span>
                            <i class="fas fa-tachometer-alt"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/products/' in request.path %}active{% endif %}" href="{% url 'products:product_list' %}">
                            <span>{% translate "Products" %}</span>
                            <i class="fas fa-box"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/inventory/' in request.path %}active{% endif %}" href="{% url 'inventory:inventory_list' %}">
                            <span>{% translate "Inventory" %}</span>
                            <i class="fas fa-warehouse"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/sales/new/' in request.path %}active{% endif %}" href="{% url 'sales:new_sale' %}">
                            <span>{% translate "New Sale" %}</span>
                            <i class="fas fa-shopping-cart"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/sales/' in request.path and not '/sales/new/' in request.path %}active{% endif %}" href="{% url 'sales:sales_list' %}">
                            <span>{% translate "Sales" %}</span>
                            <i class="fas fa-receipt"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/reports/' in request.path %}active{% endif %}" href="{% url 'reports:reports_dashboard' %}">
                            <span>{% translate "Reports" %}</span>
                            <i class="fas fa-chart-bar"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/accounts/profile/' in request.path %}active{% endif %}" href="{% url 'accounts:profile' %}">
                            <span>{% translate "Profile" %}</span>
                            <i class="fas fa-user-cog"></i>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:logout' %}">
                            <span>{% translate "Logout" %}</span>
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script>
        // Auto-dismiss alerts after 5 seconds
        $(document).ready(function() {
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
