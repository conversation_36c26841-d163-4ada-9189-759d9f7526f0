{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Edit" %} {{ product.name }} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Edit Product" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'products:product_list' %}">{% translate "Products" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'products:product_detail' product.id %}">{{ product.name }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% translate "Edit" %}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'products:product_detail' product.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Back to Product" %}
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{% translate "Edit Product Information" %}</h5>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="row mb-4">
                <div class="col-md-8">
                    <h6 class="mb-3">{% translate "Basic Information" %}</h6>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">{% translate "Product Name" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ product.name }}" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sku" class="form-label">{% translate "SKU" %} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="sku" name="sku" value="{{ product.sku }}" required>
                                <div class="form-text">{% translate "Unique product identifier" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="barcode" class="form-label">{% translate "Barcode" %}</label>
                                <input type="text" class="form-control" id="barcode" name="barcode" value="{{ product.barcode|default:'' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">{% translate "Category" %}</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">{% translate "Select Category" %}</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if product.category_id == category.id %}selected{% endif %}>{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">{% translate "Description" %}</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ product.description|default:'' }}</textarea>
                    </div>
                    
                    <h6 class="mb-3 mt-4">{% translate "Pricing Information" %}</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">{% translate "Selling Price" %} <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" value="{{ product.price }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">{% translate "Cost Price" %} <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="cost_price" name="cost_price" step="0.01" min="0" value="{{ product.cost_price }}" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% if inventory %}
                    <h6 class="mb-3 mt-4">{% translate "Inventory Information" %}</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity" class="form-label">{% translate "Current Stock" %}</label>
                                <input type="number" class="form-control" id="quantity" value="{{ inventory.quantity }}" readonly>
                                <div class="form-text">
                                    {% translate "To update stock, use the" %} 
                                    <a href="{% url 'inventory:inventory_restock' inventory.id %}">{% translate "restock" %}</a> 
                                    {% translate "function" %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reorder_level" class="form-label">{% translate "Reorder Level" %}</label>
                                <input type="number" class="form-control" id="reorder_level" name="reorder_level" min="0" value="{{ inventory.reorder_level }}">
                                <div class="form-text">{% translate "Minimum stock level before reordering" %}</div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-4">
                    <h6 class="mb-3">{% translate "Product Image" %}</h6>
                    
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="text-center mb-3">
                                {% if product.image %}
                                <img id="image-preview" src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid mb-2" style="max-height: 200px;">
                                <div id="no-image" class="bg-light d-flex align-items-center justify-content-center" style="height: 200px; display: none;">
                                    <i class="fas fa-image fa-4x text-muted"></i>
                                </div>
                                {% else %}
                                <img id="image-preview" src="#" alt="{% translate 'Preview' %}" class="img-fluid mb-2" style="max-height: 200px; display: none;">
                                <div id="no-image" class="bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-image fa-4x text-muted"></i>
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="image" class="form-label">{% translate "Upload Image" %}</label>
                                <input class="form-control" type="file" id="image" name="image" accept="image/*">
                                <div class="form-text">{% translate "Leave empty to keep current image" %}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if product.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="is_active">{% translate "Product is active" %}</label>
                    </div>
                    
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">{% translate "System Information" %}</h6>
                            <p class="card-text mb-1">
                                <small>{% translate "Created" %}: {{ product.created_at|date:"M d, Y H:i" }}</small>
                            </p>
                            <p class="card-text">
                                <small>{% translate "Last Updated" %}: {{ product.updated_at|date:"M d, Y H:i" }}</small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="border-top pt-3 text-end">
                <button type="button" class="btn btn-secondary" onclick="window.location.href='{% url 'products:product_detail' product.id %}'">
                    {% translate "Cancel" %}
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Save Changes" %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('image-preview');
                preview.src = e.target.result;
                preview.style.display = 'block';
                document.getElementById('no-image').style.display = 'none';
            }
            reader.readAsDataURL(file);
        } else {
            {% if product.image %}
            // Keep showing the current image if no new image is selected
            {% else %}
            document.getElementById('image-preview').style.display = 'none';
            document.getElementById('no-image').style.display = 'flex';
            {% endif %}
        }
    });
</script>
{% endblock %}
