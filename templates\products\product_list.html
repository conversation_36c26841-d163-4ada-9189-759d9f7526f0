{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Products" %} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Products" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <form method="get" class="d-flex">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="{% translate 'Search products...' %}" value="{{ search_query }}">
                <button class="btn btn-primary" type="submit">
                    <i class="fas fa-search"></i> {% translate "Search" %}
                </button>
            </div>
        </form>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'products:product_add' %}" class="btn btn-success">
            <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Add Product" %}
        </a>
        <a href="{% url 'products:category_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-tags {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Categories" %}
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Filter by Category" %}</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="{% url 'products:product_list' %}" class="list-group-item list-group-item-action {% if not selected_category %}active{% endif %}">
                        {% translate "All Categories" %}
                    </a>
                    {% for category in categories %}
                    <a href="{% url 'products:product_list' %}?category={{ category.id }}" class="list-group-item list-group-item-action {% if selected_category == category.id|stringformat:'s' %}active{% endif %}">
                        {{ category.name }}
                    </a>
                    {% empty %}
                    <div class="list-group-item">{% translate "No categories found" %}</div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{% translate "Product List" %}</h5>
                <span class="badge bg-primary">{{ products|length }} {% translate "products" %}</span>
            </div>
            <div class="card-body">
                {% if products %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% translate "Image" %}</th>
                                <th>{% translate "Name" %}</th>
                                <th>{% translate "SKU" %}</th>
                                <th>{% translate "Category" %}</th>
                                <th>{% translate "Price" %}</th>
                                <th>{% translate "Stock" %}</th>
                                <th>{% translate "Status" %}</th>
                                <th>{% translate "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>
                                    {% if product.image %}
                                    <img src="{{ product.image.url }}" alt="{{ product.name }}" width="50" height="50" class="img-thumbnail">
                                    {% else %}
                                    <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'products:product_detail' product.id %}">{{ product.name }}</a>
                                </td>
                                <td>{{ product.sku }}</td>
                                <td>{{ product.category.name|default:"—" }}</td>
                                <td>${{ product.price|floatformat:2 }}</td>
                                <td>
                                    {% if product.inventory %}
                                        {% if product.inventory.quantity == 0 %}
                                        <span class="badge bg-danger">{% translate "Out of Stock" %}</span>
                                        {% elif product.inventory.quantity <= product.inventory.reorder_level %}
                                        <span class="badge bg-warning">{{ product.inventory.quantity }}</span>
                                        {% else %}
                                        <span class="badge bg-success">{{ product.inventory.quantity }}</span>
                                        {% endif %}
                                    {% else %}
                                    <span class="badge bg-secondary">{% translate "No Inventory" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if product.is_active %}
                                    <span class="badge bg-success">{% translate "Active" %}</span>
                                    {% else %}
                                    <span class="badge bg-danger">{% translate "Inactive" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{% url 'products:product_edit' product.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'products:product_detail' product.id %}" class="btn btn-sm btn-info text-white">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'products:product_delete' product.id %}" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    {% if search_query %}
                    {% translate "No products found matching your search criteria." %}
                    {% else %}
                    {% translate "No products found. Add your first product using the 'Add Product' button." %}
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
