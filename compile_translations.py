import os
import polib

def compile_po_files():
    """
    Compile .po files to .mo files
    """
    languages = ['fr', 'ar']
    
    for lang in languages:
        po_file_path = os.path.join('locale', lang, 'LC_MESSAGES', 'django.po')
        mo_file_path = os.path.join('locale', lang, 'LC_MESSAGES', 'django.mo')
        
        try:
            po = polib.pofile(po_file_path)
            po.save_as_mofile(mo_file_path)
            print(f"Successfully compiled {po_file_path} to {mo_file_path}")
        except Exception as e:
            print(f"Error compiling {po_file_path}: {e}")

if __name__ == "__main__":
    compile_po_files()
