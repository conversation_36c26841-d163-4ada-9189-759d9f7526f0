{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Inventory" %}: {{ inventory.product.name }} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Inventory Details" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">{% translate "Inventory" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ inventory.product.name }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'inventory:inventory_restock' inventory.id %}" class="btn btn-success">
            <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Restock" %}
        </a>
        <a href="{% url 'inventory:inventory_edit' inventory.id %}" class="btn btn-primary">
            <i class="fas fa-edit {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Edit" %}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Product Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if inventory.product.image %}
                    <img src="{{ inventory.product.image.url }}" alt="{{ inventory.product.name }}" class="img-fluid mb-3" style="max-height: 150px;">
                    {% else %}
                    <div class="bg-light d-flex align-items-center justify-content-center mb-3" style="height: 150px;">
                        <i class="fas fa-image fa-4x text-muted"></i>
                    </div>
                    {% endif %}
                    <h5>{{ inventory.product.name }}</h5>
                    <p class="text-muted">{{ inventory.product.sku }}</p>
                </div>

                <div class="row mb-2">
                    <div class="col-6">{% translate "Category" %}:</div>
                    <div class="col-6 text-end">{{ inventory.product.category.name|default:"—" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6">{% translate "Price" %}:</div>
                    <div class="col-6 text-end">${{ inventory.product.price|floatformat:2 }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6">{% translate "Status" %}:</div>
                    <div class="col-6 text-end">
                        {% if inventory.product.is_active %}
                        <span class="badge bg-success">{% translate "Active" %}</span>
                        {% else %}
                        <span class="badge bg-danger">{% translate "Inactive" %}</span>
                        {% endif %}
                    </div>
                </div>

                <div class="d-grid mt-3">
                    <a href="{% url 'products:product_detail' inventory.product.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-box {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "View Product Details" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Inventory Status" %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light mb-3">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% translate "Current Stock" %}</h6>
                                <h3 class="mb-0">
                                    {% if inventory.quantity == 0 %}
                                    <span class="text-danger">0</span>
                                    {% elif inventory.quantity <= inventory.reorder_level %}
                                    <span class="text-warning">{{ inventory.quantity }}</span>
                                    {% else %}
                                    <span class="text-success">{{ inventory.quantity }}</span>
                                    {% endif %}
                                </h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light mb-3">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% translate "Reorder Level" %}</h6>
                                <h3 class="mb-0">{{ inventory.reorder_level }}</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">{% translate "Stock Status" %}</label>
                    <div>
                        {% if inventory.quantity == 0 %}
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar bg-danger" role="progressbar" style="width: 100%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="{{ inventory.reorder_level|add:10 }}">
                                {% translate "Out of Stock" %}
                            </div>
                        </div>
                        {% elif inventory.quantity <= inventory.reorder_level %}
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 50%;" aria-valuenow="{{ inventory.quantity }}" aria-valuemin="0" aria-valuemax="{{ inventory.reorder_level }}">
                                {% translate "Low Stock" %}
                            </div>
                        </div>
                        {% else %}
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="{{ inventory.quantity }}" aria-valuemin="0" aria-valuemax="{{ inventory.reorder_level|add:10 }}">
                                {% translate "In Stock" %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-6">{% translate "Last Restock" %}:</div>
                    <div class="col-6 text-end">
                        {% if inventory.last_restock_date %}
                        {{ inventory.last_restock_date|date:"M d, Y" }}
                        {% else %}
                        —
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-6">{% translate "Created" %}:</div>
                    <div class="col-6 text-end">{{ inventory.created_at|date:"M d, Y" }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{% translate "Stock Movement History" %}</h5>
                <span class="badge bg-primary">{{ stock_movements|length }} {% translate "entries" %}</span>
            </div>
            <div class="card-body">
                {% if stock_movements %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% translate "Date" %}</th>
                                <th>{% translate "Type" %}</th>
                                <th>{% translate "Quantity" %}</th>
                                <th>{% translate "Notes" %}</th>
                                <th>{% translate "Created By" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in stock_movements %}
                            <tr>
                                <td>{{ movement.created_at|date:"M d, Y H:i" }}</td>
                                <td>
                                    {% if movement.movement_type == 'restock' %}
                                    <span class="badge bg-success">{% translate "Restock" %}</span>
                                    {% elif movement.movement_type == 'sale' %}
                                    <span class="badge bg-primary">{% translate "Sale" %}</span>
                                    {% elif movement.movement_type == 'return' %}
                                    <span class="badge bg-warning">{% translate "Return" %}</span>
                                    {% elif movement.movement_type == 'adjustment' %}
                                    <span class="badge bg-info">{% translate "Adjustment" %}</span>
                                    {% elif movement.movement_type == 'damaged' %}
                                    <span class="badge bg-danger">{% translate "Damaged/Lost" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if movement.quantity > 0 %}
                                    <span class="text-success">+{{ movement.quantity }}</span>
                                    {% else %}
                                    <span class="text-danger">{{ movement.quantity }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ movement.notes|default:"—" }}</td>
                                <td>
                                    {% if movement.created_by %}
                                    {{ movement.created_by.user.username }}
                                    {% else %}
                                    <span class="text-muted">{% translate "System" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    {% translate "No stock movements recorded for this product yet." %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
