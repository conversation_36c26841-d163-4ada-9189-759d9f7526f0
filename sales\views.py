from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db import models
from django.http import HttpResponse
from django.template.loader import get_template
from django.utils import timezone
import json
import decimal
from .models import Sale, SaleItem
from products.models import Product
from inventory.models import Inventory

# Helper class for JSON serialization of Decimal
class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, decimal.Decimal):
            return float(obj)
        return super(DecimalEncoder, self).default(obj)

@login_required
def sales_list(request):
    sales = Sale.objects.all().order_by('-created_at')

    # Filter by status
    status = request.GET.get('status')
    if status:
        sales = sales.filter(status=status)

    # Filter by date range
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        sales = sales.filter(created_at__gte=date_from)
    if date_to:
        sales = sales.filter(created_at__lte=date_to)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        sales = sales.filter(
            models.Q(invoice_number__icontains=search_query) |
            models.Q(customer_name__icontains=search_query) |
            models.Q(customer_phone__icontains=search_query) |
            models.Q(customer_email__icontains=search_query)
        )

    context = {
        'sales': sales,
        'status': status,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
    }

    return render(request, 'sales/sales_list.html', context)

@login_required
def sale_detail(request, pk):
    sale = get_object_or_404(Sale, pk=pk)
    items = sale.items.all()

    context = {
        'sale': sale,
        'items': items,
    }

    return render(request, 'sales/sale_detail.html', context)

@login_required
def new_sale(request):
    if request.method == 'POST':
        # Process the sale
        data = json.loads(request.body)

        # Create the sale
        sale = Sale.objects.create(
            customer_name=data.get('customer_name', ''),
            customer_phone=data.get('customer_phone', ''),
            customer_email=data.get('customer_email', ''),
            cashier=request.user.profile,
            payment_method=data.get('payment_method', 'cash'),
            subtotal=data.get('subtotal', 0),
            discount=data.get('discount', 0),
            tax=data.get('tax', 0),
            notes=data.get('notes', '')
        )

        # Create sale items
        items = data.get('items', [])
        for item in items:
            product = get_object_or_404(Product, id=item['product_id'])

            # Check inventory
            try:
                inventory = Inventory.objects.get(product=product)
                if inventory.quantity < item['quantity']:
                    # Not enough stock
                    sale.delete()
                    return HttpResponse(
                        json.dumps({
                            'success': False,
                            'message': f'Not enough stock for {product.name}. Available: {inventory.quantity}'
                        }),
                        content_type='application/json'
                    )
            except Inventory.DoesNotExist:
                # No inventory record
                sale.delete()
                return HttpResponse(
                    json.dumps({
                        'success': False,
                        'message': f'No inventory record for {product.name}'
                    }),
                    content_type='application/json'
                )

            # Create sale item
            SaleItem.objects.create(
                sale=sale,
                product=product,
                quantity=item['quantity'],
                price=item['price'],
                discount=item.get('discount', 0)
            )

        # Return success response
        return HttpResponse(
            json.dumps({
                'success': True,
                'sale_id': sale.id,
                'invoice_number': sale.invoice_number
            }),
            content_type='application/json'
        )

    # GET request - show the POS interface
    products = Product.objects.filter(is_active=True)
    categories = Product.objects.values('category__name', 'category_id').distinct()

    # Get products with inventory info
    products_with_stock = []
    for product in products:
        try:
            inventory = Inventory.objects.get(product=product)
            stock = inventory.quantity
        except Inventory.DoesNotExist:
            stock = 0

        products_with_stock.append({
            'id': product.id,
            'name': product.name,
            'price': product.price,
            'category': product.category.name if product.category else 'Uncategorized',
            'category_id': product.category_id,
            'stock': stock,
            'image': product.image.url if product.image else None,
        })

    context = {
        'products': json.dumps(products_with_stock, cls=DecimalEncoder),
        'categories': categories,
    }

    return render(request, 'sales/new_sale.html', context)

@login_required
def generate_invoice(request, pk):
    # The request parameter is used by the login_required decorator
    sale = get_object_or_404(Sale, pk=pk)
    items = sale.items.all()

    context = {
        'sale': sale,
        'items': items,
        'date': timezone.now(),
    }

    # Generate PDF invoice
    template = get_template('sales/invoice_pdf.html')
    html = template.render(context)

    # For now, just render the HTML template
    # In a real implementation, you would use a PDF library like WeasyPrint or xhtml2pdf
    return HttpResponse(html)

@login_required
def refund_sale(request, pk):
    sale = get_object_or_404(Sale, pk=pk)

    if request.method == 'POST':
        refund_type = request.POST.get('refund_type', 'full')

        if refund_type == 'full':
            # Process full refund
            sale.process_refund(user=request.user.profile, full_refund=True)
            messages.success(request, f'Full refund processed for invoice #{sale.invoice_number}')
        else:
            # Process partial refund
            items = {}
            for key, value in request.POST.items():
                if key.startswith('item_') and int(value) > 0:
                    item_id = key.split('_')[1]
                    items[item_id] = int(value)

            if items:
                sale.process_refund(user=request.user.profile, full_refund=False, items=items)
                messages.success(request, f'Partial refund processed for invoice #{sale.invoice_number}')
            else:
                messages.error(request, 'No items selected for refund')
                return redirect('sales:refund_sale', pk=sale.pk)

        return redirect('sales:sale_detail', pk=sale.pk)

    context = {
        'sale': sale,
        'items': sale.items.all(),
    }

    return render(request, 'sales/refund_sale.html', context)
