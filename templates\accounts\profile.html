{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% translate "Profile" %} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "My Profile" %}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Profile Information" %}</h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    {% if profile.avatar %}
                    <img src="{{ profile.avatar.url }}" alt="{{ user.username }}" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;">
                    {% else %}
                    <div class="bg-light d-flex align-items-center justify-content-center rounded-circle mx-auto mb-3" style="width: 150px; height: 150px;">
                        <i class="fas fa-user fa-4x text-muted"></i>
                    </div>
                    {% endif %}
                </div>
                
                <h4>{{ user.get_full_name }}</h4>
                <p class="text-muted">{{ user.username }}</p>
                
                <div class="d-grid gap-2 mt-3">
                    <a href="{% url 'accounts:profile_edit' %}" class="btn btn-primary">
                        <i class="fas fa-edit {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Edit Profile" %}
                    </a>
                    <a href="{% url 'accounts:change_password' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-key {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Change Password" %}
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Account Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-5">{% translate "Role" %}:</div>
                    <div class="col-7 text-end">
                        {% if user.is_superuser %}
                        <span class="badge bg-danger">{% translate "Administrator" %}</span>
                        {% elif user.is_staff %}
                        <span class="badge bg-primary">{% translate "Staff" %}</span>
                        {% else %}
                        <span class="badge bg-secondary">{% translate "Cashier" %}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-5">{% translate "Email" %}:</div>
                    <div class="col-7 text-end">{{ user.email }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-5">{% translate "Last Login" %}:</div>
                    <div class="col-7 text-end">{{ user.last_login|date:"M d, Y H:i" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-5">{% translate "Joined" %}:</div>
                    <div class="col-7 text-end">{{ user.date_joined|date:"M d, Y" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-5">{% translate "Status" %}:</div>
                    <div class="col-7 text-end">
                        {% if user.is_active %}
                        <span class="badge bg-success">{% translate "Active" %}</span>
                        {% else %}
                        <span class="badge bg-danger">{% translate "Inactive" %}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{% translate "Recent Activity" %}</h5>
                <a href="#" class="btn btn-sm btn-outline-primary">{% translate "View All" %}</a>
            </div>
            <div class="card-body">
                {% if activities %}
                <div class="list-group">
                    {% for activity in activities %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ activity.action }}</h6>
                            <small>{{ activity.timestamp|date:"M d, Y H:i" }}</small>
                        </div>
                        <p class="mb-1">{{ activity.description }}</p>
                        <small>{{ activity.ip_address }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    {% translate "No recent activity found." %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{% translate "Recent Sales" %}</h5>
                <a href="{% url 'sales:sales_list' %}" class="btn btn-sm btn-outline-primary">{% translate "View All" %}</a>
            </div>
            <div class="card-body">
                {% if recent_sales %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% translate "Invoice #" %}</th>
                                <th>{% translate "Date" %}</th>
                                <th>{% translate "Customer" %}</th>
                                <th>{% translate "Total" %}</th>
                                <th>{% translate "Status" %}</th>
                                <th>{% translate "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in recent_sales %}
                            <tr>
                                <td>{{ sale.invoice_number }}</td>
                                <td>{{ sale.created_at|date:"M d, Y H:i" }}</td>
                                <td>{{ sale.customer_name|default:"Walk-in Customer" }}</td>
                                <td>${{ sale.total|floatformat:2 }}</td>
                                <td>
                                    {% if sale.status == 'completed' %}
                                    <span class="badge bg-success">{% translate "Completed" %}</span>
                                    {% elif sale.status == 'refunded' %}
                                    <span class="badge bg-danger">{% translate "Refunded" %}</span>
                                    {% elif sale.status == 'partially_refunded' %}
                                    <span class="badge bg-warning">{% translate "Partially Refunded" %}</span>
                                    {% elif sale.status == 'cancelled' %}
                                    <span class="badge bg-secondary">{% translate "Cancelled" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'sales:sale_detail' sale.id %}" class="btn btn-sm btn-info text-white">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    {% translate "No recent sales found." %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% translate "System Preferences" %}</h5>
            </div>
            <div class="card-body">
                <form method="post" action="{% url 'accounts:update_preferences' %}">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="language" class="form-label">{% translate "Language" %}</label>
                        <select class="form-select" id="language" name="language">
                            <option value="en" {% if LANGUAGE_CODE == 'en' %}selected{% endif %}>English</option>
                            <option value="fr" {% if LANGUAGE_CODE == 'fr' %}selected{% endif %}>Français</option>
                            <option value="ar" {% if LANGUAGE_CODE == 'ar' %}selected{% endif %}>العربية</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="theme" class="form-label">{% translate "Theme" %}</label>
                        <select class="form-select" id="theme" name="theme">
                            <option value="light" {% if profile.theme == 'light' %}selected{% endif %}>{% translate "Light" %}</option>
                            <option value="dark" {% if profile.theme == 'dark' %}selected{% endif %}>{% translate "Dark" %}</option>
                            <option value="system" {% if profile.theme == 'system' %}selected{% endif %}>{% translate "System Default" %}</option>
                        </select>
                    </div>
                    
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="notifications" name="notifications" {% if profile.notifications_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="notifications">{% translate "Enable Notifications" %}</label>
                    </div>
                    
                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Save Preferences" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
