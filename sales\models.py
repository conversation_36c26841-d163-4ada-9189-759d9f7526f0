from django.db import models
from django.utils import timezone
import uuid
from products.models import Product
from inventory.models import Inventory, StockMovement

class Sale(models.Model):
    PAYMENT_METHODS = (
        ('cash', 'Cash'),
        ('card', 'Credit/Debit Card'),
        ('transfer', 'Bank Transfer'),
        ('mobile', 'Mobile Payment'),
    )

    STATUS_CHOICES = (
        ('completed', 'Completed'),
        ('refunded', 'Refunded'),
        ('partially_refunded', 'Partially Refunded'),
        ('cancelled', 'Cancelled'),
    )

    invoice_number = models.CharField(max_length=50, unique=True, blank=True)
    customer_name = models.CharField(max_length=255, blank=True, null=True)
    customer_phone = models.CharField(max_length=20, blank=True, null=True)
    customer_email = models.EmailField(blank=True, null=True)
    cashier = models.ForeignKey('accounts.UserProfile', on_delete=models.SET_NULL, null=True, related_name='sales')
    payment_method = models.Char<PERSON><PERSON>(max_length=20, choices=PAYMENT_METHODS, default='cash')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='completed')
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    discount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tax = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Invoice #{self.invoice_number} - {self.total}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            # Generate a unique invoice number
            year = timezone.now().year
            month = timezone.now().month
            day = timezone.now().day
            unique_id = str(uuid.uuid4().hex)[:6]
            self.invoice_number = f"INV-{year}{month:02d}{day:02d}-{unique_id}"

        # Calculate total
        self.total = self.subtotal - self.discount + self.tax

        super().save(*args, **kwargs)

    def process_refund(self, user=None, full_refund=True, items=None):
        """Process a refund for this sale"""
        if full_refund:
            # Refund all items
            for item in self.items.all():
                # Return items to inventory
                inventory = item.product.inventory
                inventory.quantity += item.quantity
                inventory.save()

                # Create stock movement record
                StockMovement.objects.create(
                    inventory=inventory,
                    quantity=item.quantity,
                    movement_type='return',
                    notes=f"Refund from invoice #{self.invoice_number}",
                    created_by=user
                )

            self.status = 'refunded'
        else:
            # Partial refund for specific items
            if items:
                for item_id, qty in items.items():
                    try:
                        sale_item = self.items.get(id=item_id)
                        if qty <= sale_item.quantity:
                            # Return items to inventory
                            inventory = sale_item.product.inventory
                            inventory.quantity += qty
                            inventory.save()

                            # Create stock movement record
                            StockMovement.objects.create(
                                inventory=inventory,
                                quantity=qty,
                                movement_type='return',
                                notes=f"Partial refund from invoice #{self.invoice_number}",
                                created_by=user
                            )

                            # Update sale item quantity
                            sale_item.quantity -= qty
                            sale_item.save()
                    except SaleItem.DoesNotExist:
                        pass

                self.status = 'partially_refunded'

        self.save()
        return True

class SaleItem(models.Model):
    sale = models.ForeignKey(Sale, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    price = models.DecimalField(max_digits=10, decimal_places=2)  # Price at time of sale
    discount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"{self.product.name} x {self.quantity}"

    def save(self, *args, **kwargs):
        # Calculate total
        self.total = (self.price * self.quantity) - self.discount

        # If this is a new item (not yet saved), update inventory
        if not self.pk:
            try:
                inventory = Inventory.objects.get(product=self.product)
                if inventory.quantity >= self.quantity:
                    inventory.quantity -= self.quantity
                    inventory.save()

                    # Create stock movement record
                    StockMovement.objects.create(
                        inventory=inventory,
                        quantity=-self.quantity,
                        movement_type='sale',
                        notes=f"Sale from invoice #{self.sale.invoice_number if self.sale.invoice_number else 'New Sale'}"
                    )
            except Inventory.DoesNotExist:
                pass

        super().save(*args, **kwargs)
