from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db import models
from .models import Inventory, StockMovement
from products.models import Product

@login_required
def inventory_list(request):
    inventory_items = Inventory.objects.all().select_related('product')

    # Filter by low stock
    low_stock = request.GET.get('low_stock')
    if low_stock:
        inventory_items = inventory_items.filter(quantity__lte=models.F('reorder_level'))

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        inventory_items = inventory_items.filter(
            models.Q(product__name__icontains=search_query) |
            models.Q(product__sku__icontains=search_query)
        )

    context = {
        'inventory_items': inventory_items,
        'low_stock': low_stock,
        'search_query': search_query,
    }

    return render(request, 'inventory/inventory_list.html', context)

@login_required
def inventory_detail(request, pk):
    inventory = get_object_or_404(Inventory, pk=pk)
    stock_movements = StockMovement.objects.filter(inventory=inventory).order_by('-created_at')

    context = {
        'inventory': inventory,
        'stock_movements': stock_movements,
    }

    return render(request, 'inventory/inventory_detail.html', context)

@login_required
def inventory_add(request):
    # Get products that don't have inventory records yet
    products_without_inventory = Product.objects.exclude(
        id__in=Inventory.objects.values_list('product_id', flat=True)
    )

    if request.method == 'POST':
        product_id = request.POST.get('product')
        quantity = request.POST.get('quantity', 0)
        reorder_level = request.POST.get('reorder_level', 10)

        if not product_id:
            messages.error(request, 'Please select a product')
            return redirect('inventory:inventory_add')

        try:
            product = Product.objects.get(id=product_id)

            # Check if inventory already exists
            if Inventory.objects.filter(product=product).exists():
                messages.error(request, f'Inventory for "{product.name}" already exists')
                return redirect('inventory:inventory_add')

            # Create inventory record
            inventory = Inventory.objects.create(
                product=product,
                quantity=quantity,
                reorder_level=reorder_level
            )

            # Create stock movement record if initial quantity > 0
            if int(quantity) > 0:
                StockMovement.objects.create(
                    inventory=inventory,
                    quantity=quantity,
                    movement_type='restock',
                    notes='Initial stock'
                )

            messages.success(request, f'Inventory for "{product.name}" added successfully')
            return redirect('inventory:inventory_detail', pk=inventory.pk)

        except Product.DoesNotExist:
            messages.error(request, 'Invalid product selected')
            return redirect('inventory:inventory_add')

    context = {
        'products': products_without_inventory,
    }

    return render(request, 'inventory/inventory_add.html', context)

@login_required
def inventory_edit(request, pk):
    inventory = get_object_or_404(Inventory, pk=pk)

    if request.method == 'POST':
        reorder_level = request.POST.get('reorder_level')

        if reorder_level:
            inventory.reorder_level = reorder_level
            inventory.save()
            messages.success(request, f'Inventory for "{inventory.product.name}" updated successfully')
            return redirect('inventory:inventory_detail', pk=inventory.pk)
        else:
            messages.error(request, 'Reorder level is required')

    context = {
        'inventory': inventory,
    }

    return render(request, 'inventory/inventory_edit.html', context)

@login_required
def inventory_restock(request, pk):
    inventory = get_object_or_404(Inventory, pk=pk)

    if request.method == 'POST':
        quantity = request.POST.get('quantity')

        if not quantity or int(quantity) <= 0:
            messages.error(request, 'Please enter a valid quantity')
            return redirect('inventory:inventory_restock', pk=inventory.pk)

        # Restock inventory
        inventory.restock(int(quantity), request.user.profile)

        messages.success(request, f'Added {quantity} units to "{inventory.product.name}" inventory')
        return redirect('inventory:inventory_detail', pk=inventory.pk)

    context = {
        'inventory': inventory,
    }

    return render(request, 'inventory/inventory_restock.html', context)

@login_required
def low_stock(request):
    # Get all inventory items with quantity <= reorder_level
    low_stock_items = Inventory.objects.filter(
        quantity__lte=models.F('reorder_level')
    ).select_related('product')

    context = {
        'low_stock_items': low_stock_items,
    }

    return render(request, 'inventory/low_stock.html', context)
