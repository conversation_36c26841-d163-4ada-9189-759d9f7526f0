{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ product.name }} - {% translate "POS System" %}{% endblock %}

{% block page_title %}{% translate "Product Details" %}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'products:product_list' %}">{% translate "Products" %}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ product.name }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="{% url 'products:product_edit' product.id %}" class="btn btn-primary">
            <i class="fas fa-edit {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Edit Product" %}
        </a>
        <a href="{% url 'products:product_delete' product.id %}" class="btn btn-danger">
            <i class="fas fa-trash {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Delete" %}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-body text-center">
                {% if product.image %}
                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid mb-3" style="max-height: 200px;">
                {% else %}
                <div class="bg-light d-flex align-items-center justify-content-center mb-3" style="height: 200px;">
                    <i class="fas fa-image fa-4x text-muted"></i>
                </div>
                {% endif %}
                <h4>{{ product.name }}</h4>
                <p class="text-muted">{{ product.sku }}</p>
                <h5 class="text-primary">${{ product.price|floatformat:2 }}</h5>
                
                {% if product.is_active %}
                <span class="badge bg-success">{% translate "Active" %}</span>
                {% else %}
                <span class="badge bg-danger">{% translate "Inactive" %}</span>
                {% endif %}
                
                {% if inventory %}
                    {% if inventory.quantity == 0 %}
                    <span class="badge bg-danger">{% translate "Out of Stock" %}</span>
                    {% elif inventory.quantity <= inventory.reorder_level %}
                    <span class="badge bg-warning">{% translate "Low Stock" %}: {{ inventory.quantity }}</span>
                    {% else %}
                    <span class="badge bg-success">{% translate "In Stock" %}: {{ inventory.quantity }}</span>
                    {% endif %}
                {% else %}
                <span class="badge bg-secondary">{% translate "No Inventory" %}</span>
                {% endif %}
            </div>
        </div>
        
        {% if inventory %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Inventory Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-6">{% translate "Current Stock" %}:</div>
                    <div class="col-6 text-end fw-bold">{{ inventory.quantity }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6">{% translate "Reorder Level" %}:</div>
                    <div class="col-6 text-end">{{ inventory.reorder_level }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6">{% translate "Last Restock" %}:</div>
                    <div class="col-6 text-end">
                        {% if inventory.last_restock_date %}
                        {{ inventory.last_restock_date|date:"M d, Y" }}
                        {% else %}
                        —
                        {% endif %}
                    </div>
                </div>
                
                <div class="d-grid gap-2 mt-3">
                    <a href="{% url 'inventory:inventory_restock' inventory.id %}" class="btn btn-success">
                        <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Restock" %}
                    </a>
                    <a href="{% url 'inventory:inventory_detail' inventory.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-warehouse {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "View Inventory Details" %}
                    </a>
                </div>
            </div>
        </div>
        {% else %}
        <div class="card mb-4">
            <div class="card-body">
                <div class="alert alert-warning mb-3">
                    <i class="fas fa-exclamation-triangle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i>
                    {% translate "This product has no inventory record." %}
                </div>
                <div class="d-grid">
                    <a href="{% url 'inventory:inventory_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %}"></i> {% translate "Create Inventory Record" %}
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Product Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-3 fw-bold">{% translate "Name" %}:</div>
                    <div class="col-md-9">{{ product.name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3 fw-bold">{% translate "SKU" %}:</div>
                    <div class="col-md-9">{{ product.sku }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3 fw-bold">{% translate "Barcode" %}:</div>
                    <div class="col-md-9">{{ product.barcode|default:"—" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3 fw-bold">{% translate "Category" %}:</div>
                    <div class="col-md-9">{{ product.category.name|default:"—" }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-3 fw-bold">{% translate "Description" %}:</div>
                    <div class="col-md-9">{{ product.description|default:"—"|linebreaks }}</div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% translate "Pricing Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% translate "Selling Price" %}</h6>
                                <h4 class="text-primary">${{ product.price|floatformat:2 }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% translate "Cost Price" %}</h6>
                                <h4 class="text-secondary">${{ product.cost_price|floatformat:2 }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% translate "Profit Margin" %}</h6>
                                <h4 class="text-success">{{ product.profit_margin|floatformat:2 }}%</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% translate "System Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-md-3 fw-bold">{% translate "Created" %}:</div>
                    <div class="col-md-9">{{ product.created_at|date:"M d, Y H:i" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-md-3 fw-bold">{% translate "Last Updated" %}:</div>
                    <div class="col-md-9">{{ product.updated_at|date:"M d, Y H:i" }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-md-3 fw-bold">{% translate "Status" %}:</div>
                    <div class="col-md-9">
                        {% if product.is_active %}
                        <span class="badge bg-success">{% translate "Active" %}</span>
                        {% else %}
                        <span class="badge bg-danger">{% translate "Inactive" %}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
